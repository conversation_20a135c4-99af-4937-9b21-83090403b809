import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Shield, 
  FileText, 
  Download, 
  Trash2, 
  Edit,
  Eye,
  Calendar,
  Mail,
  ExternalLink,
  CheckCircle,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { SEOHead } from '../components/SEOHead';
import { seoConfig } from '../lib/seo-config';

export function GDPRCompliancePage() {
  const [expandedSection, setExpandedSection] = useState<string | null>(null);

  const gdprRights = [
    {
      icon: Eye,
      title: 'Right to Access',
      description: 'Request a copy of all personal data we hold about you',
      action: 'Request Data Export'
    },
    {
      icon: Edit,
      title: 'Right to Rectification',
      description: 'Correct or update your personal information',
      action: 'Update Profile'
    },
    {
      icon: Trash2,
      title: 'Right to Erasure',
      description: 'Request deletion of your personal data',
      action: 'Delete Account'
    },
    {
      icon: Download,
      title: 'Right to Portability',
      description: 'Export your data in a machine-readable format',
      action: 'Export Data'
    }
  ];

  const sections = [
    {
      id: 'gdpr-overview',
      title: 'GDPR Overview & Compliance',
      icon: Shield,
      content: [
        {
          subtitle: 'What is GDPR?',
          details: 'The General Data Protection Regulation (GDPR) is a comprehensive data protection law that came into effect on May 25, 2018. It strengthens and unifies data protection for individuals within the European Union (EU).'
        },
        {
          subtitle: 'Our Commitment',
          details: 'AppReview.Today is fully committed to GDPR compliance. We have implemented comprehensive policies, procedures, and technical measures to ensure the protection of personal data and respect for individual privacy rights.'
        },
        {
          subtitle: 'Scope of Application',
          details: 'GDPR applies to all EU residents regardless of where the data processing takes place. We extend GDPR protections to all our users worldwide as part of our commitment to privacy.'
        },
        {
          subtitle: 'Legal Basis for Processing',
          details: 'We process personal data based on legitimate interests (service provision), contractual necessity (account management), and consent (marketing communications) as defined under GDPR Article 6.'
        }
      ]
    },
    {
      id: 'data-processing',
      title: 'Data Processing & Purposes',
      icon: FileText,
      content: [
        {
          subtitle: 'Personal Data We Collect',
          details: 'We collect and process personal data including: contact information (email, name), account data (preferences, usage history), and technical data (IP address, browser information) necessary for service provision.'
        },
        {
          subtitle: 'Processing Purposes',
          details: 'Personal data is processed for: service delivery, account management, customer support, security and fraud prevention, legal compliance, and marketing communications (with consent).'
        },
        {
          subtitle: 'Data Minimization',
          details: 'We adhere to the principle of data minimization, collecting only the personal data that is necessary for the specified purposes and retaining it only as long as required.'
        },
        {
          subtitle: 'Third-Party Processing',
          details: 'When we share data with third-party processors (hosting, analytics, payment processing), we ensure they meet GDPR standards through data processing agreements and due diligence.'
        }
      ]
    },
    {
      id: 'individual-rights',
      title: 'Your Rights Under GDPR',
      icon: CheckCircle,
      content: [
        {
          subtitle: 'Right to Information',
          details: 'You have the right to be informed about how your personal data is collected, used, and shared. This information is provided in our Privacy Policy and this GDPR compliance page.'
        },
        {
          subtitle: 'Right to Access',
          details: 'You can request access to your personal data and receive a copy of the data we process about you. We provide this information free of charge within one month of your request.'
        },
        {
          subtitle: 'Right to Rectification',
          details: 'You have the right to have inaccurate personal data corrected or completed if it is incomplete. You can update most information through your account settings.'
        },
        {
          subtitle: 'Right to Erasure (Right to be Forgotten)',
          details: 'You can request deletion of your personal data when it is no longer necessary for the original purpose, you withdraw consent, or the data has been unlawfully processed.'
        },
        {
          subtitle: 'Right to Restrict Processing',
          details: 'You can request restriction of processing in certain circumstances, such as when you contest the accuracy of the data or object to processing.'
        },
        {
          subtitle: 'Right to Data Portability',
          details: 'You have the right to receive your personal data in a structured, commonly used, and machine-readable format and to transmit it to another controller.'
        },
        {
          subtitle: 'Right to Object',
          details: 'You can object to processing based on legitimate interests or for direct marketing purposes. We will stop processing unless we have compelling legitimate grounds.'
        }
      ]
    },
    {
      id: 'data-protection',
      title: 'Data Protection Measures',
      icon: Shield,
      content: [
        {
          subtitle: 'Technical Safeguards',
          details: 'We implement appropriate technical measures including encryption, access controls, secure data transmission, and regular security assessments to protect personal data.'
        },
        {
          subtitle: 'Organizational Measures',
          details: 'Our organizational measures include staff training, data protection policies, privacy impact assessments, and appointment of a Data Protection Officer (DPO).'
        },
        {
          subtitle: 'Data Breach Procedures',
          details: 'We have established procedures to detect, report, and investigate personal data breaches. We will notify supervisory authorities within 72 hours and affected individuals without undue delay when required.'
        },
        {
          subtitle: 'Privacy by Design',
          details: 'We implement privacy by design and by default, ensuring that data protection is considered from the initial design of systems and throughout the data processing lifecycle.'
        }
      ]
    },
    {
      id: 'data-transfers',
      title: 'International Data Transfers',
      icon: ExternalLink,
      content: [
        {
          subtitle: 'Transfer Mechanisms',
          details: 'When transferring personal data outside the EU/EEA, we use appropriate safeguards such as Standard Contractual Clauses (SCCs) approved by the European Commission.'
        },
        {
          subtitle: 'Adequacy Decisions',
          details: 'We may transfer data to countries with adequacy decisions from the European Commission, ensuring an adequate level of data protection.'
        },
        {
          subtitle: 'Third-Party Processors',
          details: 'Our third-party service providers who may receive personal data are contractually bound to provide adequate protection and comply with GDPR requirements.'
        },
        {
          subtitle: 'Data Localization',
          details: 'Where possible, we store and process EU residents\' data within the EU/EEA to minimize the need for international transfers.'
        }
      ]
    }
  ];

  const lastUpdated = 'January 15, 2025';

  return (
    <>
      <SEOHead {...seoConfig.gdprCompliance} />
      
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <img src="/logo.svg" alt="AppReview.Today" className="h-8 w-8" />
                <span className="text-xl font-bold text-gray-900">AppReview.Today</span>
              </div>
              <button 
                onClick={() => window.history.back()}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Back to Home
              </button>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Shield className="h-16 w-16 text-blue-600 mx-auto mb-6" />
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                GDPR <span className="text-blue-600">Compliance</span>
              </h1>
              <p className="text-xl text-gray-600 mb-6">
                We are committed to protecting your privacy and ensuring full compliance with the General Data Protection Regulation.
              </p>
              <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
                <Calendar className="h-4 w-4" />
                <span>Last updated: {lastUpdated}</span>
              </div>
            </motion.div>
          </div>
        </section>

        {/* GDPR Rights */}
        <section className="py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">Your GDPR Rights</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {gdprRights.map((right, index) => {
                const Icon = right.icon;
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="bg-white rounded-xl p-6 shadow-sm border text-center"
                  >
                    <Icon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{right.title}</h3>
                    <p className="text-gray-600 text-sm mb-4">{right.description}</p>
                    <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                      {right.action}
                    </button>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Detailed Sections */}
        <section className="py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="space-y-6">
              {sections.map((section, index) => {
                const Icon = section.icon;
                const isExpanded = expandedSection === section.id;
                
                return (
                  <motion.div
                    key={section.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="bg-white rounded-xl shadow-sm border"
                  >
                    <button
                      onClick={() => setExpandedSection(isExpanded ? null : section.id)}
                      className="w-full flex items-center justify-between p-6 text-left hover:bg-gray-50"
                    >
                      <div className="flex items-center space-x-4">
                        <Icon className="h-6 w-6 text-blue-600" />
                        <h3 className="text-lg font-semibold text-gray-900">{section.title}</h3>
                      </div>
                      {isExpanded ? 
                        <ChevronDown className="h-5 w-5 text-gray-400" /> :
                        <ChevronRight className="h-5 w-5 text-gray-400" />
                      }
                    </button>
                    
                    {isExpanded && (
                      <div className="border-t border-gray-200 p-6 bg-gray-50">
                        <div className="space-y-6">
                          {section.content.map((item, itemIndex) => (
                            <div key={itemIndex}>
                              <h4 className="font-semibold text-gray-900 mb-2">{item.subtitle}</h4>
                              <p className="text-gray-600 leading-relaxed">{item.details}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </motion.div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Contact DPO */}
        <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Data Protection Officer</h2>
            <p className="text-xl text-gray-600 mb-8">
              For any GDPR-related questions or to exercise your rights, contact our Data Protection Officer.
            </p>
            
            <div className="bg-blue-50 border border-blue-200 rounded-xl p-6 max-w-md mx-auto">
              <Mail className="h-8 w-8 text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Data Protection Officer</h3>
              <p className="text-gray-600 mb-4"><EMAIL></p>
              <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                Contact DPO
              </button>
            </div>
            
            <div className="mt-8 text-sm text-gray-500">
              <p>We will respond to your GDPR requests within one month. Complex requests may take up to three months with notification.</p>
            </div>
          </div>
        </section>
      </div>
    </>
  );
}
