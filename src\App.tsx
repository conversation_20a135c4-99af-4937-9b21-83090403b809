import React, { useEffect, Suspense } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Analytics } from '@vercel/analytics/react'
import { HelmetProvider } from 'react-helmet-async'
import { useAuthStore } from './stores/authStore'
import { LoadingSpinner } from './components/LoadingSpinner'
import { PaymentStatusBanner } from './components/PaymentStatusBanner'
import { PerformanceMonitor } from './components/PerformanceMonitor'
import { queryClientConfig, initializeCacheCleanup } from './lib/cache-config'
import { initializeMobileOptimizations } from './lib/mobile-optimization'

// Lazy load pages for better performance
const LandingPage = React.lazy(() => import('./pages/LandingPage').then(module => ({ default: module.LandingPage })))
const ReportPage = React.lazy(() => import('./pages/ReportPage').then(module => ({ default: module.ReportPage })))
const ProfilePage = React.lazy(() => import('./pages/ProfilePage').then(module => ({ default: module.ProfilePage })))
const DemoPage = React.lazy(() => import('./pages/DemoPage').then(module => ({ default: module.DemoPage })))
const PricingPage = React.lazy(() => import('./pages/PricingPage').then(module => ({ default: module.PricingPage })))
const PaymentSuccessPage = React.lazy(() => import('./pages/PaymentSuccessPage').then(module => ({ default: module.PaymentSuccessPage })))
const AboutPage = React.lazy(() => import('./pages/AboutPage').then(module => ({ default: module.AboutPage })))
const FAQPage = React.lazy(() => import('./pages/FAQPage').then(module => ({ default: module.FAQPage })))
const HowItWorksPage = React.lazy(() => import('./pages/HowItWorksPage').then(module => ({ default: module.HowItWorksPage })))
const FeaturesPage = React.lazy(() => import('./pages/FeaturesPage').then(module => ({ default: module.FeaturesPage })))
const UseCasesPage = React.lazy(() => import('./pages/UseCasesPage').then(module => ({ default: module.UseCasesPage })))

const HelpCenterPage = React.lazy(() => import('./pages/HelpCenterPage').then(module => ({ default: module.HelpCenterPage })))
const GettingStartedPage = React.lazy(() => import('./pages/GettingStartedPage').then(module => ({ default: module.GettingStartedPage })))

const PrivacyPolicyPage = React.lazy(() => import('./pages/PrivacyPolicyPage').then(module => ({ default: module.PrivacyPolicyPage })))
const TermsOfServicePage = React.lazy(() => import('./pages/TermsOfServicePage').then(module => ({ default: module.TermsOfServicePage })))
const SecurityPage = React.lazy(() => import('./pages/SecurityPage').then(module => ({ default: module.SecurityPage })))
const GDPRCompliancePage = React.lazy(() => import('./pages/GDPRCompliancePage').then(module => ({ default: module.GDPRCompliancePage })))
const ContactPage = React.lazy(() => import('./pages/ContactPage').then(module => ({ default: module.ContactPage })))
const BlogPage = React.lazy(() => import('./pages/BlogPage').then(module => ({ default: module.BlogPage })))
const BlogPostPage = React.lazy(() => import('./pages/BlogPostPage').then(module => ({ default: module.BlogPostPage })))
const BlogCategoryPage = React.lazy(() => import('./pages/BlogCategoryPage').then(module => ({ default: module.BlogCategoryPage })))
const BlogTagPage = React.lazy(() => import('./pages/BlogTagPage').then(module => ({ default: module.BlogTagPage })))

// User Role Pages
const ForProductManagersPage = React.lazy(() => import('./pages/ForProductManagersPage').then(module => ({ default: module.ForProductManagersPage })))
const ForMarketersPage = React.lazy(() => import('./pages/ForMarketersPage').then(module => ({ default: module.ForMarketersPage })))
const ForResearchersPage = React.lazy(() => import('./pages/ForResearchersPage').then(module => ({ default: module.ForResearchersPage })))

// Industry Vertical Pages
const GamingAppsPage = React.lazy(() => import('./pages/GamingAppsPage').then(module => ({ default: module.GamingAppsPage })))
const EcommerceAppsPage = React.lazy(() => import('./pages/EcommerceAppsPage').then(module => ({ default: module.EcommerceAppsPage })))
const FintechAppsPage = React.lazy(() => import('./pages/FintechAppsPage').then(module => ({ default: module.FintechAppsPage })))

// Sitemap Page
const SitemapPage = React.lazy(() => import('./pages/SitemapPage').then(module => ({ default: module.SitemapPage })))

const queryClient = new QueryClient(queryClientConfig)

function App() {
  const { initialize, loading } = useAuthStore()

  useEffect(() => {
    initialize()
    initializeCacheCleanup()
    initializeMobileOptimizations()
  }, [initialize])

  if (loading) {
    return <LoadingSpinner message="Initializing..." />
  }

  return (
    <HelmetProvider>
      <QueryClientProvider client={queryClient}>
        <Router
          future={{
            v7_startTransition: true,
            v7_relativeSplatPath: true,
          }}
        >
        <PaymentStatusBanner />
        <Suspense fallback={<LoadingSpinner message="Loading page..." />}>
          <Routes>
            <Route path="/" element={<LandingPage />} />
            <Route path="/pricing" element={<PricingPage />} />
            <Route path="/payment-success" element={<ProtectedRoute><PaymentSuccessPage /></ProtectedRoute>} />
            <Route path="/report/:reportId" element={<ReportPage />} />
            <Route path="/profile" element={<ProtectedRoute><ProfilePage /></ProtectedRoute>} />
            <Route path="/demo" element={<DemoPage />} />
            <Route path="/about" element={<AboutPage />} />
            <Route path="/faq" element={<FAQPage />} />
            <Route path="/how-it-works" element={<HowItWorksPage />} />
            <Route path="/features" element={<FeaturesPage />} />
            <Route path="/use-cases" element={<UseCasesPage />} />

            <Route path="/help" element={<HelpCenterPage />} />
            <Route path="/getting-started" element={<GettingStartedPage />} />

            <Route path="/privacy" element={<PrivacyPolicyPage />} />
            <Route path="/terms" element={<TermsOfServicePage />} />
            <Route path="/security" element={<SecurityPage />} />
            <Route path="/gdpr" element={<GDPRCompliancePage />} />
            <Route path="/contact" element={<ContactPage />} />
            <Route path="/blog" element={<BlogPage />} />
            <Route path="/blog/:slug" element={<BlogPostPage />} />
            <Route path="/blog/category/:categorySlug" element={<BlogCategoryPage />} />
            <Route path="/blog/tag/:tagSlug" element={<BlogTagPage />} />
            <Route path="/for-product-managers" element={<ForProductManagersPage />} />
            <Route path="/for-marketers" element={<ForMarketersPage />} />
            <Route path="/for-researchers" element={<ForResearchersPage />} />

            {/* Industry Vertical Pages */}
            <Route path="/gaming-apps" element={<GamingAppsPage />} />
            <Route path="/ecommerce-apps" element={<EcommerceAppsPage />} />
            <Route path="/fintech-apps" element={<FintechAppsPage />} />

            {/* Sitemap */}
            <Route path="/sitemap" element={<SitemapPage />} />

            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Suspense>
        </Router>
        <Analytics />
        <PerformanceMonitor />
      </QueryClientProvider>
    </HelmetProvider>
  )
}

const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading } = useAuthStore()
  
  // Show loading while authentication is being checked
  if (loading) {
    return <LoadingSpinner message="Checking authentication..." />
  }
  
  // Only redirect if we're sure the user is not authenticated
  if (!user) {
    return <Navigate to="/" replace />
  }
  
  return children
}

export default App