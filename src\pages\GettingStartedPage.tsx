import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Play, 
  CheckCircle, 
  Circle,
  ArrowRight,
  Clock,
  Users,
  Zap,
  Target,
  BarChart3,
  Settings,
  Download,
  ExternalLink
} from 'lucide-react';
import { SEOHead } from '../components/SEOHead';
import { seoConfig } from '../lib/seo-config';

export function GettingStartedPage() {
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);

  const toggleStep = (stepIndex: number) => {
    setCompletedSteps(prev => 
      prev.includes(stepIndex) 
        ? prev.filter(i => i !== stepIndex)
        : [...prev, stepIndex]
    );
  };

  const steps = [
    {
      title: 'Create Your Account',
      description: 'Sign up for AppReview.Today and verify your email address',
      duration: '2 minutes',
      details: [
        'Visit our signup page and enter your details',
        'Choose your preferred plan (start with free trial)',
        'Verify your email address',
        'Complete your profile setup'
      ]
    },
    {
      title: 'Connect Your App',
      description: 'Link your app from App Store, Google Play, or other platforms',
      duration: '5 minutes',
      details: [
        'Navigate to the "Add App" section',
        'Enter your app\'s URL or search by name',
        'Select the correct app from search results',
        'Configure review collection settings'
      ]
    },
    {
      title: 'Start Your First Analysis',
      description: 'Run your first review analysis to see the platform in action',
      duration: '3 minutes',
      details: [
        'Click "Start Analysis" on your app dashboard',
        'Choose analysis parameters (date range, platforms)',
        'Wait for AI processing to complete',
        'Review the generated insights and reports'
      ]
    },
    {
      title: 'Explore Key Features',
      description: 'Discover sentiment analysis, themes, and actionable insights',
      duration: '10 minutes',
      details: [
        'Review sentiment distribution and trends',
        'Explore automatically extracted themes',
        'Check competitor comparison data',
        'Download your first PDF report'
      ]
    },
    {
      title: 'Set Up Monitoring',
      description: 'Configure alerts and automated reports for ongoing insights',
      duration: '5 minutes',
      details: [
        'Enable real-time review monitoring',
        'Set up email alerts for sentiment changes',
        'Configure weekly/monthly report delivery',
        'Customize notification preferences'
      ]
    },
    {
      title: 'Invite Your Team',
      description: 'Add team members and configure collaboration settings',
      duration: '3 minutes',
      details: [
        'Go to Team Settings in your dashboard',
        'Send invitations to team members',
        'Assign appropriate roles and permissions',
        'Set up shared workspaces and reports'
      ]
    }
  ];

  const quickTips = [
    {
      icon: Zap,
      title: 'Start Small',
      description: 'Begin with one app to understand the platform before adding more'
    },
    {
      icon: Target,
      title: 'Focus on Trends',
      description: 'Pay attention to sentiment trends over time, not just current ratings'
    },
    {
      icon: BarChart3,
      title: 'Use Filters',
      description: 'Filter reviews by date, rating, or platform for deeper insights'
    },
    {
      icon: Users,
      title: 'Share Insights',
      description: 'Export reports and share findings with your team regularly'
    }
  ];

  const resources = [
    {
      title: 'Video Tutorial Series',
      description: 'Watch our comprehensive video guides',
      duration: '15 min total',
      type: 'video',
      link: '#'
    },
    {
      title: 'Sample Analysis Report',
      description: 'Download a sample report to see what to expect',
      duration: 'PDF',
      type: 'download',
      link: '#'
    },
    {
      title: 'API Documentation',
      description: 'Technical documentation for developers',
      duration: 'Reference',
      type: 'docs',
      link: '#'
    },
    {
      title: 'Best Practices Guide',
      description: 'Learn how successful teams use AppReview.Today',
      duration: '10 min read',
      type: 'guide',
      link: '#'
    }
  ];

  const completionPercentage = Math.round((completedSteps.length / steps.length) * 100);

  return (
    <>
      <SEOHead {...seoConfig.gettingStarted} />
      
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <img src="/logo.svg" alt="AppReview.Today" className="h-8 w-8" />
                <span className="text-xl font-bold text-gray-900">AppReview.Today</span>
              </div>
              <button 
                onClick={() => window.history.back()}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Back to Home
              </button>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Play className="h-16 w-16 text-green-600 mx-auto mb-6" />
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                Getting <span className="text-green-600">Started</span>
              </h1>
              <p className="text-xl text-gray-600 mb-8">
                Welcome to AppReview.Today! Follow this step-by-step guide to get up and running in under 30 minutes.
              </p>
              
              {/* Progress Bar */}
              <div className="max-w-md mx-auto">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Progress</span>
                  <span className="text-sm font-medium text-green-600">{completionPercentage}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <motion.div 
                    className="bg-green-600 h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${completionPercentage}%` }}
                    transition={{ duration: 0.5 }}
                  />
                </div>
                <p className="text-sm text-gray-500 mt-2">
                  {completedSteps.length} of {steps.length} steps completed
                </p>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Steps */}
        <section className="py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">Setup Steps</h2>
            
            <div className="space-y-6">
              {steps.map((step, index) => {
                const isCompleted = completedSteps.includes(index);
                const StepIcon = isCompleted ? CheckCircle : Circle;
                
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className={`bg-white rounded-xl p-6 shadow-sm border-2 transition-all ${
                      isCompleted ? 'border-green-200 bg-green-50' : 'border-gray-200 hover:border-blue-200'
                    }`}
                  >
                    <div className="flex items-start space-x-4">
                      <button
                        onClick={() => toggleStep(index)}
                        className={`flex-shrink-0 mt-1 ${
                          isCompleted ? 'text-green-600' : 'text-gray-400 hover:text-blue-600'
                        }`}
                      >
                        <StepIcon className="h-6 w-6" />
                      </button>
                      
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className={`text-lg font-semibold ${
                            isCompleted ? 'text-green-900' : 'text-gray-900'
                          }`}>
                            Step {index + 1}: {step.title}
                          </h3>
                          <div className="flex items-center space-x-2 text-sm text-gray-500">
                            <Clock className="h-4 w-4" />
                            <span>{step.duration}</span>
                          </div>
                        </div>
                        
                        <p className={`mb-4 ${
                          isCompleted ? 'text-green-700' : 'text-gray-600'
                        }`}>
                          {step.description}
                        </p>
                        
                        <ul className="space-y-2">
                          {step.details.map((detail, detailIndex) => (
                            <li key={detailIndex} className="flex items-start space-x-2">
                              <ArrowRight className={`h-4 w-4 mt-0.5 flex-shrink-0 ${
                                isCompleted ? 'text-green-600' : 'text-gray-400'
                              }`} />
                              <span className={`text-sm ${
                                isCompleted ? 'text-green-700' : 'text-gray-600'
                              }`}>
                                {detail}
                              </span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Quick Tips */}
        <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">Quick Tips for Success</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {quickTips.map((tip, index) => {
                const Icon = tip.icon;
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="bg-gray-50 rounded-xl p-6 text-center"
                  >
                    <Icon className="h-8 w-8 text-blue-600 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{tip.title}</h3>
                    <p className="text-gray-600 text-sm">{tip.description}</p>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Resources */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">Additional Resources</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {resources.map((resource, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white rounded-xl p-6 shadow-sm border hover:shadow-md transition-shadow cursor-pointer"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">{resource.title}</h3>
                      <p className="text-gray-600 mb-3">{resource.description}</p>
                      <span className="text-sm text-gray-500">{resource.duration}</span>
                    </div>
                    <ExternalLink className="h-5 w-5 text-gray-400 ml-4 flex-shrink-0" />
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA */}
        <section className="py-16 px-4 sm:px-6 lg:px-8 bg-blue-600">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-white mb-4">Ready to Get Started?</h2>
            <p className="text-xl text-blue-100 mb-8">
              Create your account today and start analyzing your app reviews in minutes.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                Start Free Trial
              </button>
              <button className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                Watch Demo
              </button>
            </div>
          </div>
        </section>
      </div>
    </>
  );
}
