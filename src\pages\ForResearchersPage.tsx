import React from 'react';
import { motion } from 'framer-motion';
import { Search, BarChart3, Database, FileText, TrendingUp, Users, CheckCircle, ArrowRight, BookOpen, ExternalLink } from 'lucide-react';
import { SEOHead } from '../components/SEOHead';
import { LazyImage } from '../components/LazyImage';
import { Footer } from '../components/Footer';

export const ForResearchersPage: React.FC = () => {
  const features = [
    {
      icon: <Search className="w-8 h-8 text-indigo-600" />,
      title: "Advanced Analytics",
      description: "Comprehensive statistical analysis of user feedback patterns, sentiment trends, and behavioral insights across multiple dimensions."
    },
    {
      icon: <Database className="w-8 h-8 text-green-600" />,
      title: "Data Export & API",
      description: "Export raw data in multiple formats (CSV, JSON, Excel) or access via API for integration with your research tools and workflows."
    },
    {
      icon: <BarChart3 className="w-8 h-8 text-blue-600" />,
      title: "Statistical Modeling",
      description: "Built-in statistical tools for correlation analysis, trend forecasting, and hypothesis testing on user feedback data."
    },
    {
      icon: <FileText className="w-8 h-8 text-purple-600" />,
      title: "Research Reports",
      description: "Generate comprehensive research reports with visualizations, statistical summaries, and publication-ready charts."
    }
  ];

  const researchAreas = [
    {
      title: "User Experience Research",
      description: "Analyze user satisfaction patterns and identify UX improvement opportunities",
      methods: ["Sentiment analysis", "Feature satisfaction scoring", "User journey mapping", "Pain point identification"],
      applications: ["Academic UX studies", "Industry research", "Comparative analysis", "Longitudinal studies"]
    },
    {
      title: "Market Research",
      description: "Study market trends, competitive landscapes, and consumer behavior patterns",
      methods: ["Competitive sentiment analysis", "Market positioning studies", "Consumer preference analysis", "Trend forecasting"],
      applications: ["Market reports", "Industry analysis", "Consumer insights", "Strategic planning"]
    },
    {
      title: "Product Research",
      description: "Investigate product performance, feature adoption, and user feedback patterns",
      methods: ["Feature impact analysis", "Product lifecycle studies", "User adoption patterns", "Quality assessment"],
      applications: ["Product development", "Innovation research", "Quality studies", "Performance analysis"]
    }
  ];

  const tools = [
    {
      name: "Data Explorer",
      description: "Interactive data exploration with filtering, segmentation, and visualization tools",
      capabilities: ["Multi-dimensional filtering", "Custom date ranges", "Cohort analysis", "Real-time updates"]
    },
    {
      name: "Statistical Suite",
      description: "Comprehensive statistical analysis tools for research-grade insights",
      capabilities: ["Correlation analysis", "Regression modeling", "Hypothesis testing", "Confidence intervals"]
    },
    {
      name: "Visualization Studio",
      description: "Create publication-ready charts and visualizations for research presentations",
      capabilities: ["Custom chart types", "Interactive dashboards", "Export options", "Branding customization"]
    },
    {
      name: "Report Generator",
      description: "Automated research report generation with statistical summaries and insights",
      capabilities: ["Template library", "Custom formatting", "Citation management", "Collaborative editing"]
    }
  ];

  const metrics = [
    { value: "500M+", label: "Data Points Analyzed", description: "Comprehensive dataset for research" },
    { value: "95%", label: "Statistical Accuracy", description: "Research-grade data quality" },
    { value: "50+", label: "Research Papers Published", description: "Academic validation" },
    { value: "24/7", label: "Data Availability", description: "Always-on research access" }
  ];

  const publications = [
    {
      title: "User Sentiment Patterns in Mobile App Ecosystems",
      journal: "Journal of Digital Innovation",
      year: "2024",
      authors: "Dr. Sarah Chen, Prof. Michael Rodriguez",
      citation: "Chen, S., & Rodriguez, M. (2024). User Sentiment Patterns in Mobile App Ecosystems. Journal of Digital Innovation, 15(3), 45-62."
    },
    {
      title: "Predictive Models for App Store Success",
      journal: "International Conference on HCI",
      year: "2023",
      authors: "Prof. Lisa Wang, Dr. James Park",
      citation: "Wang, L., & Park, J. (2023). Predictive Models for App Store Success. Proceedings of the International Conference on HCI, 234-248."
    }
  ];

  const blogArticles = [
    {
      title: "MVP Validation Through Early User Reviews",
      excerpt: "Learn how to use early user reviews to validate your MVP, identify product-market fit signals, and make data-driven decisions for your startup.",
      slug: "mvp-validation-early-user-reviews",
      readTime: "16 min read",
      category: "Research Methods"
    },
    {
      title: "Longitudinal User Behavior Analysis from Reviews",
      excerpt: "Discover how to conduct rigorous longitudinal studies using app review data to understand user behavior evolution and product lifecycle patterns.",
      slug: "longitudinal-user-behavior-analysis-reviews",
      readTime: "22 min read",
      category: "Research Methodology"
    },
    {
      title: "Startup App Launch: Review Strategy Guide",
      excerpt: "A comprehensive guide for startups on leveraging app reviews for successful product launches, from pre-launch preparation to post-launch optimization.",
      slug: "startup-app-launch-review-strategy-guide",
      readTime: "24 min read",
      category: "Launch Strategy"
    },
    {
      title: "Investor Pitch: Using Review Data as Traction Proof",
      excerpt: "Learn how to leverage app review data as compelling traction evidence in investor pitches. Transform user feedback into powerful metrics that demonstrate product-market fit.",
      slug: "investor-pitch-using-review-data-traction-proof",
      readTime: "25 min read",
      category: "Fundraising"
    },
    {
      title: "Bootstrap vs Funded: Review Analysis Priorities",
      excerpt: "Discover how bootstrap and funded startups should prioritize review analysis differently. Learn resource allocation strategies and focus areas for maximum impact.",
      slug: "bootstrap-vs-funded-review-analysis-priorities",
      readTime: "26 min read",
      category: "Strategy"
    }
  ];

  return (
    <>
      <SEOHead 
        title="For Researchers - AppReview.Today | Academic & Market Research Platform"
        description="Advanced analytics platform for researchers. Statistical modeling, data export, research reports, and publication-ready insights for academic and market research."
        canonical="/for-researchers"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebPage",
          "name": "AppReview.Today for Researchers",
          "description": "Research analytics platform designed for academic researchers and market analysts",
          "audience": {
            "@type": "Audience",
            "audienceType": "Researchers"
          }
        }}
      />
      
      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-indigo-900 via-purple-900 to-blue-900 text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center"
            >
              <div className="flex justify-center mb-6">
                <Search className="w-16 h-16 text-indigo-400" />
              </div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Research Platform for <span className="text-indigo-400">Academics</span>
              </h1>
              <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto text-indigo-100">
                Advanced analytics and statistical tools for user experience research, market studies, 
                and academic publications. Research-grade data quality and methodology.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                  Access Research Platform
                </button>
                <button className="border border-indigo-400 text-indigo-400 hover:bg-indigo-400 hover:text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                  View Publications
                </button>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Research Features */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Research-Grade Analytics
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Comprehensive tools and methodologies for rigorous academic and market research
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow"
                >
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      {feature.icon}
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-3">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Research Areas */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Research Applications
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Explore diverse research opportunities across multiple disciplines
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {researchAreas.map((area, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-gray-50 p-8 rounded-xl"
                >
                  <h3 className="text-xl font-bold text-gray-900 mb-3">
                    {area.title}
                  </h3>
                  <p className="text-gray-600 mb-6">
                    {area.description}
                  </p>
                  
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-3">Research Methods:</h4>
                    <div className="space-y-2">
                      {area.methods.map((method, methodIndex) => (
                        <div key={methodIndex} className="flex items-center gap-3">
                          <CheckCircle className="w-4 h-4 text-green-600 flex-shrink-0" />
                          <span className="text-gray-700 text-sm">{method}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">Applications:</h4>
                    <div className="space-y-2">
                      {area.applications.map((application, appIndex) => (
                        <div key={appIndex} className="flex items-center gap-3">
                          <div className="w-2 h-2 bg-indigo-600 rounded-full flex-shrink-0"></div>
                          <span className="text-gray-700 text-sm">{application}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Research Tools */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Research Toolkit
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Comprehensive suite of tools for data analysis, visualization, and reporting
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {tools.map((tool, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white p-8 rounded-xl shadow-lg"
                >
                  <h3 className="text-xl font-bold text-gray-900 mb-3">
                    {tool.name}
                  </h3>
                  <p className="text-gray-600 mb-6">
                    {tool.description}
                  </p>
                  <div className="space-y-2">
                    {tool.capabilities.map((capability, capIndex) => (
                      <div key={capIndex} className="flex items-center gap-3">
                        <CheckCircle className="w-4 h-4 text-green-600 flex-shrink-0" />
                        <span className="text-gray-700">{capability}</span>
                      </div>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Metrics */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Research Impact
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Trusted by researchers worldwide for high-quality data and insights
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {metrics.map((metric, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-gray-50 p-8 rounded-xl text-center"
                >
                  <div className="text-4xl font-bold text-indigo-600 mb-2">
                    {metric.value}
                  </div>
                  <div className="text-lg font-semibold text-gray-900 mb-2">
                    {metric.label}
                  </div>
                  <div className="text-gray-600 text-sm">
                    {metric.description}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Publications */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Featured Publications
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Research powered by AppReview.Today data and analytics
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {publications.map((publication, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white p-8 rounded-xl shadow-lg"
                >
                  <h3 className="text-xl font-bold text-gray-900 mb-3">
                    {publication.title}
                  </h3>
                  <div className="text-gray-600 mb-2">
                    <span className="font-semibold">{publication.journal}</span> • {publication.year}
                  </div>
                  <div className="text-gray-600 mb-4">
                    {publication.authors}
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-700 font-mono">
                      {publication.citation}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Research Resources */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <div className="flex justify-center mb-4">
                <BookOpen className="w-12 h-12 text-indigo-600" />
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Research Methodologies & Guides
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Comprehensive guides for conducting rigorous research with user review data
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {blogArticles.map((article, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-gray-50 p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow group cursor-pointer"
                  onClick={() => window.open(`/blog/${article.slug}`, '_blank')}
                >
                  <div className="flex items-start justify-between mb-4">
                    <span className="bg-indigo-100 text-indigo-700 px-3 py-1 rounded-full text-sm font-medium">
                      {article.category}
                    </span>
                    <span className="text-gray-500 text-sm">{article.readTime}</span>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-indigo-600 transition-colors">
                    {article.title}
                  </h3>
                  <p className="text-gray-600 mb-4 text-lg">
                    {article.excerpt}
                  </p>
                  <div className="flex items-center text-indigo-600 font-semibold group-hover:text-indigo-700 transition-colors">
                    <span>Read Research Guide</span>
                    <ExternalLink className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-indigo-600">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Start Your Research?
              </h2>
              <p className="text-xl text-indigo-100 mb-8 max-w-2xl mx-auto">
                Join the growing community of researchers using AppReview.Today for groundbreaking studies.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-white text-indigo-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold transition-colors flex items-center gap-2">
                  Request Research Access
                  <ArrowRight className="w-4 h-4" />
                </button>
                <button className="border border-white text-white hover:bg-white hover:text-indigo-600 px-8 py-3 rounded-lg font-semibold transition-colors">
                  Download Sample Data
                </button>
              </div>
            </motion.div>
          </div>
        </section>
      </div>

      <Footer />
    </>
  );
};
