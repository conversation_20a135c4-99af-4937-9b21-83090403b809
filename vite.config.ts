import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  base: '/', // Changed from '/feedbacklens/' for Vercel
  build: {
    outDir: 'dist', // Changed from 'docs' to standard 'dist' for Vercel
    emptyOutDir: true,
    // Performance optimizations
    rollupOptions: {
      output: {
        // Manual chunk splitting for better caching
        manualChunks: {
          // Vendor chunks
          'react-vendor': ['react', 'react-dom'],
          'router-vendor': ['react-router-dom'],
          'query-vendor': ['@tanstack/react-query'],
          'ui-vendor': ['framer-motion', 'lucide-react'],
          'supabase-vendor': ['@supabase/supabase-js'],
          'analytics-vendor': ['@vercel/analytics'],

          // Page chunks
          'pages-main': [
            './src/pages/LandingPage.tsx',
            './src/pages/PricingPage.tsx'
          ],
          'pages-content': [
            './src/pages/AboutPage.tsx',
            './src/pages/FAQPage.tsx',
            './src/pages/HowItWorksPage.tsx'
          ],
          'pages-app': [
            './src/pages/ProfilePage.tsx',
            './src/pages/ReportPage.tsx',
            './src/pages/DemoPage.tsx'
          ]
        }
      }
    },
    // Optimize chunk size
    chunkSizeWarningLimit: 1000,
    // Enable source maps for production debugging
    sourcemap: false,
    // Minification options
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  },
  optimizeDeps: {
    exclude: ['lucide-react'],
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@tanstack/react-query',
      '@supabase/supabase-js'
    ]
  },
  // Development server optimizations
  server: {
    hmr: {
      overlay: false
    }
  },
  // Preview server optimizations
  preview: {
    port: 4173,
    strictPort: true
  }
});
