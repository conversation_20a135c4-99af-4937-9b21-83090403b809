import React from 'react'
import { motion } from 'framer-motion'
import { 
  Smartphone, 
  MessageSquare, 
  Code, 
  Webhook,
  Database,
  Cloud,
  Zap,
  Settings,
  ArrowRight,
  CheckCircle,
  ExternalLink,
  Play,
  Download,
  Bell
} from 'lucide-react'
import { SEOHead } from '../components/SEOHead'
import { seoConfig } from '../lib/seo-config'
import { Footer } from '../components/Footer'

const platforms = [
  {
    name: "App Store",
    description: "Analyze iOS app reviews from Apple's App Store",
    icon: "🍎",
    features: ["Real-time review monitoring", "Rating trend analysis", "Version-specific feedback", "Localized review support"],
    status: "Available",
    setupTime: "5 minutes"
  },
  {
    name: "Google Play",
    description: "Extract insights from Android app reviews",
    icon: "🤖",
    features: ["Automated review collection", "Developer response tracking", "Beta feedback analysis", "Multi-language support"],
    status: "Available",
    setupTime: "5 minutes"
  },
  {
    name: "Reddit",
    description: "Monitor community discussions and feedback",
    icon: "🔴",
    features: ["Subreddit monitoring", "Sentiment tracking", "Community engagement analysis", "Trend detection"],
    status: "Available",
    setupTime: "10 minutes"
  },
  {
    name: "Steam",
    description: "Gaming platform review analysis",
    icon: "🎮",
    features: ["Game review analysis", "Player sentiment tracking", "Update impact analysis", "Community feedback"],
    status: "Coming Soon",
    setupTime: "TBD"
  }
]

const tools = [
  {
    category: "Communication",
    items: [
      {
        name: "Slack",
        description: "Get review alerts and reports directly in your Slack channels",
        icon: "💬",
        features: ["Real-time notifications", "Custom alert rules", "Report sharing", "Team collaboration"],
        status: "Available",
        type: "webhook"
      },
      {
        name: "Microsoft Teams",
        description: "Integrate review insights into your Teams workflow",
        icon: "👥",
        features: ["Automated reports", "Alert notifications", "Team discussions", "Dashboard sharing"],
        status: "Available",
        type: "webhook"
      },
      {
        name: "Discord",
        description: "Community-focused review monitoring for gaming apps",
        icon: "🎯",
        features: ["Server notifications", "Community alerts", "Bot integration", "Custom commands"],
        status: "Beta",
        type: "webhook"
      }
    ]
  },
  {
    category: "Project Management",
    items: [
      {
        name: "Jira",
        description: "Automatically create tickets from critical review feedback",
        icon: "📋",
        features: ["Auto-ticket creation", "Priority assignment", "Bug tracking", "Sprint integration"],
        status: "Available",
        type: "api"
      },
      {
        name: "Trello",
        description: "Add review insights to your project boards",
        icon: "📌",
        features: ["Card creation", "Board automation", "Label assignment", "Due date setting"],
        status: "Available",
        type: "api"
      },
      {
        name: "Asana",
        description: "Transform feedback into actionable tasks",
        icon: "✅",
        features: ["Task automation", "Project tracking", "Team assignments", "Progress monitoring"],
        status: "Coming Soon",
        type: "api"
      }
    ]
  },
  {
    category: "Analytics & BI",
    items: [
      {
        name: "Google Analytics",
        description: "Connect review sentiment with app usage data",
        icon: "📊",
        features: ["Custom events", "Goal tracking", "Audience insights", "Conversion analysis"],
        status: "Available",
        type: "api"
      },
      {
        name: "Mixpanel",
        description: "Correlate review feedback with user behavior",
        icon: "📈",
        features: ["Event tracking", "Funnel analysis", "Cohort studies", "A/B test insights"],
        status: "Available",
        type: "api"
      },
      {
        name: "Tableau",
        description: "Create advanced visualizations of review data",
        icon: "📉",
        features: ["Custom dashboards", "Data blending", "Advanced analytics", "Report automation"],
        status: "Beta",
        type: "api"
      }
    ]
  }
]

const apiFeatures = [
  {
    title: "RESTful API",
    description: "Simple, intuitive REST API for all review analysis functions",
    features: ["JSON responses", "Rate limiting", "Authentication", "Comprehensive documentation"]
  },
  {
    title: "Webhooks",
    description: "Real-time notifications for new reviews and analysis updates",
    features: ["Custom endpoints", "Retry logic", "Payload customization", "Security headers"]
  },
  {
    title: "SDKs",
    description: "Official SDKs for popular programming languages",
    features: ["Python SDK", "Node.js SDK", "PHP SDK", "Go SDK (Coming Soon)"]
  },
  {
    title: "GraphQL",
    description: "Flexible data querying with GraphQL endpoint",
    features: ["Custom queries", "Real-time subscriptions", "Type safety", "Introspection"]
  }
]

export const IntegrationsPage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = React.useState("Communication")

  return (
    <>
      <SEOHead {...seoConfig.integrations} />
      
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
        {/* Header */}
        <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <img src="/app-review-today.svg" alt="AppReview.Today" className="h-8 w-8" />
                <span className="text-xl font-bold text-gray-900">AppReview.Today</span>
              </div>
              <button 
                onClick={() => window.location.href = '/'}
                className="px-4 py-2 text-blue-600 hover:text-blue-700 font-medium transition-colors"
              >
                Back to Home
              </button>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <div className="flex items-center justify-center mb-6">
                <Zap className="h-8 w-8 text-purple-600 mr-3" />
                <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
                  <span className="text-purple-600">Integrations</span> & API
                </h1>
              </div>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                Connect AppReview.Today with your existing tools and workflows. 
                Seamlessly integrate review analysis into your development, marketing, and business processes.
              </p>
              
              {/* Quick Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto mb-12">
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600">20+</div>
                  <div className="text-sm text-gray-600">Integrations</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">99.9%</div>
                  <div className="text-sm text-gray-600">API Uptime</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">&lt;100ms</div>
                  <div className="text-sm text-gray-600">Response Time</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-orange-600">24/7</div>
                  <div className="text-sm text-gray-600">Support</div>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Platform Integrations */}
        <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white/50">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Platform Integrations
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Connect with major app stores and platforms to automatically collect and analyze reviews.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {platforms.map((platform, index) => (
                <motion.div
                  key={platform.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow p-6 border border-gray-100"
                >
                  <div className="text-center mb-4">
                    <div className="text-4xl mb-2">{platform.icon}</div>
                    <h3 className="text-xl font-semibold text-gray-900">{platform.name}</h3>
                    <p className="text-gray-600 text-sm mt-2">{platform.description}</p>
                  </div>
                  
                  <div className="space-y-2 mb-4">
                    {platform.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center text-sm text-gray-700">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      platform.status === 'Available' 
                        ? 'bg-green-100 text-green-800' 
                        : platform.status === 'Beta'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {platform.status}
                    </span>
                    <span className="text-xs text-gray-500">{platform.setupTime}</span>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Tool Integrations */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Tool Integrations
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Integrate with your favorite tools to streamline workflows and automate processes.
              </p>
            </div>
            
            {/* Category Tabs */}
            <div className="flex flex-wrap justify-center gap-2 mb-8">
              {tools.map((category) => (
                <button
                  key={category.category}
                  onClick={() => setSelectedCategory(category.category)}
                  className={`px-6 py-2 rounded-full text-sm font-medium transition-colors ${
                    selectedCategory === category.category
                      ? 'bg-purple-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {category.category}
                </button>
              ))}
            </div>
            
            {/* Tool Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {tools.find(cat => cat.category === selectedCategory)?.items.map((tool, index) => (
                <motion.div
                  key={tool.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow p-6 border border-gray-100"
                >
                  <div className="flex items-center mb-4">
                    <div className="text-3xl mr-4">{tool.icon}</div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900">{tool.name}</h3>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        tool.status === 'Available' 
                          ? 'bg-green-100 text-green-800' 
                          : tool.status === 'Beta'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {tool.status}
                      </span>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 mb-4">{tool.description}</p>
                  
                  <div className="space-y-2 mb-4">
                    {tool.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center text-sm text-gray-700">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                    <span className="text-xs text-gray-500 capitalize">{tool.type} Integration</span>
                    <button className="text-purple-600 hover:text-purple-700 text-sm font-medium flex items-center">
                      Setup Guide <ExternalLink className="h-3 w-3 ml-1" />
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* API Features */}
        <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white/50">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Developer API
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Build custom integrations with our comprehensive API and developer tools.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {apiFeatures.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow p-6 border border-gray-100"
                >
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 mb-4">{feature.description}</p>
                  
                  <div className="space-y-2">
                    {feature.features.map((item, idx) => (
                      <div key={idx} className="flex items-center text-sm text-gray-700">
                        <Code className="h-4 w-4 text-purple-500 mr-2 flex-shrink-0" />
                        {item}
                      </div>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
            
            <div className="text-center mt-12">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="px-8 py-3 bg-purple-600 text-white rounded-lg font-semibold hover:bg-purple-700 transition-colors flex items-center justify-center">
                  <Code className="h-5 w-5 mr-2" />
                  View API Docs
                </button>
                <button className="px-8 py-3 bg-transparent border-2 border-purple-600 text-purple-600 rounded-lg font-semibold hover:bg-purple-600 hover:text-white transition-colors flex items-center justify-center">
                  <Download className="h-5 w-5 mr-2" />
                  Download SDKs
                </button>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-purple-600 to-blue-600">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Integrate?
            </h2>
            <p className="text-xl text-purple-100 mb-8">
              Start connecting AppReview.Today with your existing tools and workflows today.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button 
                onClick={() => window.location.href = '/demo'}
                className="px-8 py-3 bg-white text-purple-600 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                Start Free Trial
              </button>
              <button 
                onClick={() => window.location.href = '/contact'}
                className="px-8 py-3 bg-transparent border-2 border-white text-white rounded-lg font-semibold hover:bg-white hover:text-purple-600 transition-colors"
              >
                Contact Sales
              </button>
            </div>
          </div>
        </section>
      </div>

      <Footer />
    </>
  )
}
