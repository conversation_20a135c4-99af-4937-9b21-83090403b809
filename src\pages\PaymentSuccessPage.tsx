import React, { useEffect } from 'react'
import { motion } from 'framer-motion'
import { BarChart3, <PERSON>Circle, ArrowR<PERSON> } from 'lucide-react'
import { Button } from '../components/ui/Button'
import { Card } from '../components/ui/Card'
import { useAuthStore } from '../stores/authStore'
import { useNavigate } from 'react-router-dom'

export const PaymentSuccessPage: React.FC = () => {
  const { user, refreshUserUsage } = useAuthStore()
  const navigate = useNavigate()

  useEffect(() => {
    // Refresh user usage to reflect the new limits
    if (user) {
      // Add a small delay to ensure webhook has processed
      setTimeout(() => {
        refreshUserUsage()
      }, 2000)
    }
  }, [user, refreshUserUsage])

  const handleGoToProfile = () => {
    navigate('/profile')
  }

  const handleCreateReport = () => {
    navigate('/demo')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0A1128] via-[#0F1B3C] to-[#0A1128] flex items-center justify-center">
      {/* Header */}
      <header className="fixed top-0 left-0 right-0 border-b border-white/10 backdrop-blur-sm z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 py-4 flex items-center justify-between">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center space-x-2 cursor-pointer"
            onClick={() => navigate('/')}
          >
            <BarChart3 className="w-6 h-6 sm:w-8 sm:h-8 text-[#2DD4BF]" />
            <span className="text-lg sm:text-xl font-bold text-white">AppReview.Today</span>
          </motion.div>
        </div>
      </header>

      {/* Main Content */}
      <div className="pt-20 px-4 sm:px-6 w-full max-w-2xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 30, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.6 }}
        >
          <Card className="text-center">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="mb-6"
            >
              <div className="w-20 h-20 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-10 h-10 text-green-400" />
              </div>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-4"
            >
              Payment Successful!
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="text-white/70 text-base sm:text-lg mb-6"
            >
              Thank you for your purchase! Your additional report credits have been added to your account.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="bg-[#2DD4BF]/10 border border-[#2DD4BF]/20 rounded-lg p-4 mb-8"
            >
              <p className="text-[#2DD4BF] font-medium mb-2">
                🎉 Credits Added Successfully
              </p>
              <p className="text-white/60 text-sm">
                It may take a few moments for your new credits to appear in your account.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="flex flex-col sm:flex-row gap-4"
            >
              <Button
                variant="primary"
                size="lg"
                onClick={handleCreateReport}
                className="flex-1 flex items-center justify-center"
              >
                <span>Create Report</span>
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>

              <Button
                variant="secondary"
                size="lg"
                onClick={handleGoToProfile}
                className="flex-1"
              >
                View Profile
              </Button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="mt-6 pt-6 border-t border-white/10"
            >
              <p className="text-white/50 text-sm">
                Need help? Contact us at{' '}
                <a href="mailto:<EMAIL>" className="text-[#2DD4BF] hover:underline">
                  <EMAIL>
                </a>
              </p>
            </motion.div>
          </Card>
        </motion.div>
      </div>
    </div>
  )
} 