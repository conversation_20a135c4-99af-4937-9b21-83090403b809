import React from 'react';
import { motion } from 'framer-motion';
import { BarChart3, ExternalLink, FileText, Users, Building2, BookOpen, HelpCircle, Shield } from 'lucide-react';
import { SEOHead } from '../components/SEOHead';
import { useNavigate } from 'react-router-dom';
import { Button } from '../components/ui/Button';

interface SitemapSection {
  title: string;
  icon: React.ReactNode;
  links: Array<{
    title: string;
    url: string;
    description: string;
  }>;
}

export const SitemapPage: React.FC = () => {
  const navigate = useNavigate();

  const sitemapSections: SitemapSection[] = [
    {
      title: 'Main Pages',
      icon: <FileText className="h-5 w-5" />,
      links: [
        { title: 'Home', url: '/', description: 'AI-powered app review analysis platform' },
        { title: 'Pricing', url: '/pricing', description: 'Flexible pricing plans for every team size' },
        { title: 'Demo', url: '/demo', description: 'Try our platform with interactive demos' },
        { title: 'Features', url: '/features', description: 'Comprehensive feature overview' },
        { title: 'How It Works', url: '/how-it-works', description: 'Learn how our AI analysis works' },
        { title: 'Use Cases', url: '/use-cases', description: 'Real-world applications and examples' },

      ]
    },
    {
      title: 'User Solutions',
      icon: <Users className="h-5 w-5" />,
      links: [
        { title: 'For Product Managers', url: '/for-product-managers', description: 'Product development insights and workflows' },
        { title: 'For Marketers', url: '/for-marketers', description: 'Marketing strategies and campaign optimization' },
        { title: 'For Researchers', url: '/for-researchers', description: 'Academic research tools and methodologies' },
      ]
    },
    {
      title: 'Industry Solutions',
      icon: <Building2 className="h-5 w-5" />,
      links: [
        { title: 'Gaming Apps', url: '/gaming-apps', description: 'Player sentiment and game optimization' },
        { title: 'E-commerce Apps', url: '/ecommerce-apps', description: 'Customer experience optimization' },
        { title: 'Fintech Apps', url: '/fintech-apps', description: 'Financial app user experience analysis' },
      ]
    },
    {
      title: 'Resources & Content',
      icon: <BookOpen className="h-5 w-5" />,
      links: [
        { title: 'Blog', url: '/blog', description: 'Latest insights and industry trends' },
        { title: 'Getting Started', url: '/getting-started', description: 'Quick start guide and tutorials' },
        { title: 'API Documentation', url: '/api-docs', description: 'Developer resources and API reference' },
        { title: 'Troubleshooting', url: '/troubleshooting', description: 'Common issues and solutions' },
        { title: 'Help Center', url: '/help', description: 'Comprehensive help and support' },
      ]
    },
    {
      title: 'Support & Information',
      icon: <HelpCircle className="h-5 w-5" />,
      links: [
        { title: 'FAQ', url: '/faq', description: 'Frequently asked questions' },
        { title: 'About Us', url: '/about', description: 'Our mission, team, and company story' },
        { title: 'Contact', url: '/contact', description: 'Get in touch with our team' },
      ]
    },
    {
      title: 'Legal & Compliance',
      icon: <Shield className="h-5 w-5" />,
      links: [
        { title: 'Privacy Policy', url: '/privacy', description: 'How we protect your data' },
        { title: 'Terms of Service', url: '/terms', description: 'Terms and conditions of use' },
        { title: 'Security', url: '/security', description: 'Our security practices and measures' },
        { title: 'GDPR Compliance', url: '/gdpr', description: 'GDPR compliance information' },
      ]
    }
  ];

  return (
    <>
      <SEOHead
        title="Sitemap - AppReview.Today | Site Navigation"
        description="Complete sitemap of AppReview.Today - find all pages, resources, and tools for AI-powered app review analysis."
        keywords="sitemap, site navigation, app review analysis, mobile app insights"
        url="https://appreview.today/sitemap"
        type="website"
      />
      
      <div className="min-h-screen bg-gradient-to-br from-[#0A1128] via-[#0F1B3C] to-[#0A1128]">
        {/* Header */}
        <header className="border-b border-white/10 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 py-4 flex items-center justify-between">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="flex items-center space-x-2 cursor-pointer"
              onClick={() => navigate('/')}
            >
              <BarChart3 className="h-8 w-8 text-blue-400" />
              <span className="text-xl font-bold text-white">
                AppReview.Today
              </span>
            </motion.div>
            <Button onClick={() => navigate('/')}>
              Back to Home
            </Button>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <FileText className="h-16 w-16 text-blue-400 mx-auto mb-6" />
              <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
                Site <span className="text-blue-400">Navigation</span>
              </h1>
              <p className="text-xl text-gray-300 mb-8">
                Explore all pages and resources on AppReview.Today
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="/sitemap.xml"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View XML Sitemap
                </a>
                <a
                  href="/robots.txt"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View Robots.txt
                </a>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Sitemap Sections */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {sitemapSections.map((section, sectionIndex) => (
                <motion.div
                  key={section.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: sectionIndex * 0.1 }}
                  className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10"
                >
                  <div className="flex items-center space-x-3 mb-6">
                    <div className="p-2 bg-blue-500/20 rounded-lg text-blue-400">
                      {section.icon}
                    </div>
                    <h2 className="text-xl font-semibold text-white">
                      {section.title}
                    </h2>
                  </div>
                  
                  <div className="space-y-4">
                    {section.links.map((link, linkIndex) => (
                      <motion.div
                        key={link.url}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3, delay: (sectionIndex * 0.1) + (linkIndex * 0.05) }}
                        className="group"
                      >
                        <button
                          onClick={() => navigate(link.url)}
                          className="w-full text-left p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-colors border border-transparent hover:border-white/20"
                        >
                          <div className="flex items-center justify-between">
                            <div>
                              <h3 className="font-medium text-white group-hover:text-blue-400 transition-colors">
                                {link.title}
                              </h3>
                              <p className="text-sm text-gray-400 mt-1">
                                {link.description}
                              </p>
                            </div>
                            <ExternalLink className="h-4 w-4 text-gray-500 group-hover:text-blue-400 transition-colors" />
                          </div>
                        </button>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="border-t border-white/10 py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <p className="text-gray-400">
              © 2025 AppReview.Today. All rights reserved.
            </p>
            <div className="mt-4 flex justify-center space-x-6">
              <a href="/sitemap.xml" className="text-gray-400 hover:text-white transition-colors">
                XML Sitemap
              </a>
              <a href="/robots.txt" className="text-gray-400 hover:text-white transition-colors">
                Robots.txt
              </a>
              <a href="/llms.txt" className="text-gray-400 hover:text-white transition-colors">
                AI Info
              </a>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
};
