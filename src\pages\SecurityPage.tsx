import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Shield, 
  Lock, 
  Key, 
  Server, 
  Eye,
  AlertTriangle,
  CheckCircle,
  Calendar,
  Mail,
  ExternalLink,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { SEOHead } from '../components/SEOHead';
import { seoConfig } from '../lib/seo-config';

export function SecurityPage() {
  const [expandedSection, setExpandedSection] = useState<string | null>(null);

  const securityFeatures = [
    {
      icon: Lock,
      title: 'End-to-End Encryption',
      description: 'All data transmission uses TLS 1.3 encryption with AES-256 encryption at rest'
    },
    {
      icon: Key,
      title: 'Multi-Factor Authentication',
      description: 'Optional 2FA support with TOTP and SMS verification for enhanced account security'
    },
    {
      icon: Server,
      title: 'Secure Infrastructure',
      description: 'Hosted on enterprise-grade cloud infrastructure with 99.9% uptime SLA'
    },
    {
      icon: Eye,
      title: 'Privacy by Design',
      description: 'Built with privacy-first principles and minimal data collection practices'
    }
  ];

  const sections = [
    {
      id: 'data-protection',
      title: 'Data Protection & Encryption',
      icon: Lock,
      content: [
        {
          subtitle: 'Encryption in Transit',
          details: 'All data transmitted between your browser and our servers is protected using TLS 1.3 encryption with perfect forward secrecy. This ensures that even if encryption keys are compromised, past communications remain secure.'
        },
        {
          subtitle: 'Encryption at Rest',
          details: 'All stored data is encrypted using AES-256 encryption. Database encryption keys are managed separately from the data and rotated regularly according to industry best practices.'
        },
        {
          subtitle: 'Key Management',
          details: 'We use industry-standard key management systems with hardware security modules (HSMs) to protect encryption keys. Keys are never stored in plaintext and access is strictly controlled.'
        },
        {
          subtitle: 'Data Anonymization',
          details: 'Personal identifiers in review data are anonymized or pseudonymized where possible to protect user privacy while maintaining analytical value.'
        }
      ]
    },
    {
      id: 'access-controls',
      title: 'Access Controls & Authentication',
      icon: Key,
      content: [
        {
          subtitle: 'Multi-Factor Authentication',
          details: 'We support TOTP-based 2FA and SMS verification to add an extra layer of security to your account. We strongly recommend enabling 2FA for all accounts.'
        },
        {
          subtitle: 'Role-Based Access Control',
          details: 'Our system implements granular role-based permissions to ensure users only have access to the data and features they need for their role.'
        },
        {
          subtitle: 'Session Management',
          details: 'User sessions are secured with cryptographically strong session tokens that expire automatically. Sessions are invalidated on logout and after periods of inactivity.'
        },
        {
          subtitle: 'Password Security',
          details: 'Passwords are hashed using bcrypt with salt rounds. We enforce strong password requirements and monitor for compromised credentials.'
        }
      ]
    },
    {
      id: 'infrastructure',
      title: 'Infrastructure Security',
      icon: Server,
      content: [
        {
          subtitle: 'Cloud Security',
          details: 'Our infrastructure is hosted on enterprise-grade cloud platforms with SOC 2 Type II compliance, providing robust physical and network security controls.'
        },
        {
          subtitle: 'Network Security',
          details: 'All network traffic is protected by firewalls, intrusion detection systems, and DDoS protection. We use VPCs and private subnets to isolate sensitive components.'
        },
        {
          subtitle: 'Regular Updates',
          details: 'Our systems are regularly updated with the latest security patches. We maintain automated vulnerability scanning and patch management processes.'
        },
        {
          subtitle: 'Backup & Recovery',
          details: 'Data is backed up regularly with encrypted backups stored in geographically distributed locations. We maintain tested disaster recovery procedures.'
        }
      ]
    },
    {
      id: 'monitoring',
      title: 'Security Monitoring & Incident Response',
      icon: Eye,
      content: [
        {
          subtitle: 'Continuous Monitoring',
          details: 'We employ 24/7 security monitoring with automated threat detection, anomaly detection, and real-time alerting for suspicious activities.'
        },
        {
          subtitle: 'Audit Logging',
          details: 'All system access and user activities are logged with tamper-evident audit trails. Logs are retained according to compliance requirements and analyzed for security events.'
        },
        {
          subtitle: 'Incident Response',
          details: 'We maintain a formal incident response plan with defined procedures for detecting, containing, and recovering from security incidents. Users are notified promptly of any breaches affecting their data.'
        },
        {
          subtitle: 'Vulnerability Management',
          details: 'Regular security assessments, penetration testing, and vulnerability scans are conducted by third-party security firms to identify and address potential weaknesses.'
        }
      ]
    },
    {
      id: 'compliance',
      title: 'Compliance & Certifications',
      icon: CheckCircle,
      content: [
        {
          subtitle: 'GDPR Compliance',
          details: 'We are fully compliant with the General Data Protection Regulation (GDPR) and provide tools for data subject rights including access, portability, and deletion.'
        },
        {
          subtitle: 'SOC 2 Type II',
          details: 'Our infrastructure providers maintain SOC 2 Type II compliance, ensuring robust controls for security, availability, and confidentiality.'
        },
        {
          subtitle: 'ISO 27001',
          details: 'We follow ISO 27001 information security management standards and are working toward formal certification.'
        },
        {
          subtitle: 'Regular Audits',
          details: 'We undergo regular security audits and assessments by independent third parties to validate our security controls and compliance posture.'
        }
      ]
    }
  ];

  const lastUpdated = 'January 15, 2025';

  return (
    <>
      <SEOHead {...seoConfig.security} />
      
      <div className="min-h-screen bg-gradient-to-br from-[#0A1128] via-[#0F1B3C] to-[#0A1128]">
        {/* Header */}
        <header className="border-b border-white/10 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <img src="/logo.svg" alt="AppReview.Today" className="h-8 w-8" />
                <span className="text-xl font-bold text-white">AppReview.Today</span>
              </div>
              <button
                onClick={() => window.history.back()}
                className="px-4 py-2 text-sm font-medium text-white bg-white/10 rounded-lg hover:bg-white/20 transition-colors backdrop-blur-sm border border-white/20"
              >
                Back to Home
              </button>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Shield className="h-16 w-16 text-green-600 mx-auto mb-6" />
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                Security & <span className="text-green-600">Trust</span>
              </h1>
              <p className="text-xl text-gray-600 mb-6">
                Your data security is our top priority. Learn about our comprehensive security measures and compliance standards.
              </p>
              <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
                <Calendar className="h-4 w-4" />
                <span>Last updated: {lastUpdated}</span>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Security Features */}
        <section className="py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">Security Features</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {securityFeatures.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="bg-white rounded-xl p-6 shadow-sm border text-center"
                  >
                    <Icon className="h-12 w-12 text-green-600 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{feature.title}</h3>
                    <p className="text-gray-600 text-sm">{feature.description}</p>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Detailed Sections */}
        <section className="py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="space-y-6">
              {sections.map((section, index) => {
                const Icon = section.icon;
                const isExpanded = expandedSection === section.id;
                
                return (
                  <motion.div
                    key={section.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="bg-white rounded-xl shadow-sm border"
                  >
                    <button
                      onClick={() => setExpandedSection(isExpanded ? null : section.id)}
                      className="w-full flex items-center justify-between p-6 text-left hover:bg-gray-50"
                    >
                      <div className="flex items-center space-x-4">
                        <Icon className="h-6 w-6 text-green-600" />
                        <h3 className="text-lg font-semibold text-gray-900">{section.title}</h3>
                      </div>
                      {isExpanded ? 
                        <ChevronDown className="h-5 w-5 text-gray-400" /> :
                        <ChevronRight className="h-5 w-5 text-gray-400" />
                      }
                    </button>
                    
                    {isExpanded && (
                      <div className="border-t border-gray-200 p-6 bg-gray-50">
                        <div className="space-y-6">
                          {section.content.map((item, itemIndex) => (
                            <div key={itemIndex}>
                              <h4 className="font-semibold text-gray-900 mb-2">{item.subtitle}</h4>
                              <p className="text-gray-600 leading-relaxed">{item.details}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </motion.div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Security Contact */}
        <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Security Concerns?</h2>
            <p className="text-xl text-gray-600 mb-8">
              If you discover a security vulnerability or have security-related questions, please contact us immediately.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl mx-auto">
              <div className="bg-red-50 border border-red-200 rounded-xl p-6">
                <AlertTriangle className="h-8 w-8 text-red-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Report Vulnerability</h3>
                <p className="text-gray-600 mb-4"><EMAIL></p>
                <button className="w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors">
                  Report Issue
                </button>
              </div>
              
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
                <Mail className="h-8 w-8 text-blue-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Security Questions</h3>
                <p className="text-gray-600 mb-4"><EMAIL></p>
                <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                  Contact Security Team
                </button>
              </div>
            </div>
            
            <div className="mt-8 text-sm text-gray-500">
              <p>We follow responsible disclosure practices and will acknowledge security reports within 24 hours.</p>
            </div>
          </div>
        </section>
      </div>
    </>
  );
}
