import React from 'react';
import { motion } from 'framer-motion';
import { Target, BarChart3, Users, Lightbulb, TrendingUp, Star, CheckCircle, ArrowRight, BookOpen, ExternalLink } from 'lucide-react';
import { SEOHead } from '../components/SEOHead';
import { LazyImage } from '../components/LazyImage';
import { Footer } from '../components/Footer';

export const ForProductManagersPage: React.FC = () => {
  const features = [
    {
      icon: <Target className="w-8 h-8 text-blue-600" />,
      title: "Strategic Insights",
      description: "Transform user feedback into actionable product strategy. Identify feature gaps, prioritize roadmap items, and validate product decisions."
    },
    {
      icon: <BarChart3 className="w-8 h-8 text-green-600" />,
      title: "Competitive Analysis",
      description: "Compare your app's user sentiment against competitors. Understand market positioning and identify differentiation opportunities."
    },
    {
      icon: <Users className="w-8 h-8 text-purple-600" />,
      title: "User Journey Mapping",
      description: "Map user feedback to specific journey stages. Optimize onboarding, feature adoption, and retention strategies."
    },
    {
      icon: <Lightbulb className="w-8 h-8 text-yellow-600" />,
      title: "Feature Validation",
      description: "Validate feature ideas before development. Measure feature impact and user satisfaction post-launch."
    }
  ];

  const workflows = [
    {
      title: "Product Discovery",
      description: "Identify unmet user needs and feature opportunities from review analysis",
      steps: ["Analyze user pain points", "Identify feature requests", "Prioritize by impact", "Create user stories"],
      outcome: "Data-driven product roadmap"
    },
    {
      title: "Launch Planning",
      description: "Plan feature launches based on user feedback patterns and market demand",
      steps: ["Validate feature concepts", "Assess market readiness", "Plan rollout strategy", "Set success metrics"],
      outcome: "Higher feature adoption rates"
    },
    {
      title: "Performance Monitoring",
      description: "Track product performance through continuous user sentiment analysis",
      steps: ["Monitor satisfaction scores", "Track feature mentions", "Identify issues early", "Measure improvements"],
      outcome: "Proactive issue resolution"
    }
  ];

  const metrics = [
    { value: "73%", label: "Faster Feature Validation", description: "Validate product ideas before development" },
    { value: "2.4x", label: "Better Roadmap Prioritization", description: "Data-driven decision making" },
    { value: "45%", label: "Improved User Satisfaction", description: "Address user needs effectively" },
    { value: "60%", label: "Reduced Development Waste", description: "Build features users actually want" }
  ];

  const testimonials = [
    {
      quote: "AppReview.Today transformed how we prioritize our product roadmap. We now make decisions based on actual user needs, not assumptions.",
      author: "Sarah Chen",
      role: "Senior Product Manager",
      company: "TechFlow",
      avatar: "/images/testimonials/sarah.jpg"
    },
    {
      quote: "The competitive analysis feature helped us identify a key differentiator that increased our app store rating by 0.8 points.",
      author: "Michael Rodriguez",
      role: "Head of Product",
      company: "AppVenture",
      avatar: "/images/testimonials/michael.jpg"
    }
  ];

  const blogArticles = [
    {
      title: "Product Roadmap Planning with Review Insights",
      excerpt: "Learn how to leverage user review data to build data-driven product roadmaps that align with real user needs and market demands.",
      slug: "product-roadmap-planning-review-insights",
      readTime: "12 min read",
      category: "Product Strategy"
    },
    {
      title: "Feature Request Analysis and Prioritization",
      excerpt: "Master the art of analyzing feature requests from user reviews and prioritizing them effectively to maximize user satisfaction and business impact.",
      slug: "feature-request-analysis-prioritization",
      readTime: "15 min read",
      category: "Feature Management"
    },
    {
      title: "Competitive Intelligence Through Review Monitoring",
      excerpt: "Learn how to systematically monitor competitor reviews to identify market opportunities, feature gaps, and strategic advantages.",
      slug: "competitive-intelligence-review-monitoring",
      readTime: "20 min read",
      category: "Competitive Analysis"
    },
    {
      title: "User Journey Mapping Through Reviews",
      excerpt: "Discover how to create comprehensive user journey maps using app review data to identify pain points, optimize touchpoints, and improve user experience.",
      slug: "user-journey-mapping-through-reviews",
      readTime: "19 min read",
      category: "User Experience"
    },
    {
      title: "Product-Market Fit Indicators in App Reviews",
      excerpt: "Learn how to identify and measure product-market fit signals hidden in app reviews, from user language patterns to engagement indicators.",
      slug: "product-market-fit-indicators-app-reviews",
      readTime: "21 min read",
      category: "Product Strategy"
    },
    {
      title: "Competitive Feature Analysis Guide",
      excerpt: "Master the art of analyzing competitor features through app reviews. Learn systematic approaches to identify feature gaps, user preferences, and strategic opportunities.",
      slug: "competitive-feature-analysis-guide",
      readTime: "23 min read",
      category: "Competitive Analysis"
    }
  ];

  return (
    <>
      <SEOHead 
        title="For Product Managers - AppReview.Today | Strategic Product Insights"
        description="Transform user feedback into product strategy. Competitive analysis, feature validation, user journey mapping, and data-driven roadmap prioritization for product managers."
        canonical="/for-product-managers"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebPage",
          "name": "AppReview.Today for Product Managers",
          "description": "Strategic product insights platform designed for product managers and product teams",
          "audience": {
            "@type": "Audience",
            "audienceType": "Product Managers"
          }
        }}
      />
      
      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center"
            >
              <div className="flex justify-center mb-6">
                <Target className="w-16 h-16 text-purple-400" />
              </div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Strategic Insights for <span className="text-purple-400">Product Managers</span>
              </h1>
              <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto text-purple-100">
                Transform user feedback into winning product strategy. Make data-driven decisions, 
                validate features faster, and build products users love.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                  Start Strategic Analysis
                </button>
                <button className="border border-purple-400 text-purple-400 hover:bg-purple-400 hover:text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                  View Case Studies
                </button>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Key Features */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Product Management Superpowers
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Everything you need to make informed product decisions and drive user satisfaction
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow"
                >
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      {feature.icon}
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-3">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Product Workflows */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Streamlined Product Workflows
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Integrate user feedback analysis into your existing product management processes
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {workflows.map((workflow, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-gray-50 p-8 rounded-xl"
                >
                  <h3 className="text-xl font-bold text-gray-900 mb-3">
                    {workflow.title}
                  </h3>
                  <p className="text-gray-600 mb-6">
                    {workflow.description}
                  </p>
                  <div className="space-y-3 mb-6">
                    {workflow.steps.map((step, stepIndex) => (
                      <div key={stepIndex} className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                        <span className="text-gray-700">{step}</span>
                      </div>
                    ))}
                  </div>
                  <div className="bg-purple-100 p-4 rounded-lg">
                    <div className="flex items-center gap-2 text-purple-700 font-semibold">
                      <TrendingUp className="w-4 h-4" />
                      {workflow.outcome}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Metrics */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Proven Results for Product Teams
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                See how product managers are achieving better outcomes with data-driven insights
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {metrics.map((metric, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white p-8 rounded-xl shadow-lg text-center"
                >
                  <div className="text-4xl font-bold text-purple-600 mb-2">
                    {metric.value}
                  </div>
                  <div className="text-lg font-semibold text-gray-900 mb-2">
                    {metric.label}
                  </div>
                  <div className="text-gray-600 text-sm">
                    {metric.description}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Testimonials */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Trusted by Product Leaders
              </h2>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {testimonials.map((testimonial, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-gray-50 p-8 rounded-xl"
                >
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <blockquote className="text-gray-700 mb-6 text-lg">
                    "{testimonial.quote}"
                  </blockquote>
                  <div className="flex items-center gap-4">
                    <LazyImage
                      src={testimonial.avatar}
                      alt={testimonial.author}
                      className="w-12 h-12 rounded-full"
                    />
                    <div>
                      <div className="font-semibold text-gray-900">{testimonial.author}</div>
                      <div className="text-gray-600">{testimonial.role}, {testimonial.company}</div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Expert Resources */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <div className="flex justify-center mb-4">
                <BookOpen className="w-12 h-12 text-purple-600" />
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Expert Resources for Product Managers
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Deep-dive guides and strategies to master review-driven product management
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {blogArticles.map((article, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow group cursor-pointer"
                  onClick={() => window.open(`/blog/${article.slug}`, '_blank')}
                >
                  <div className="flex items-start justify-between mb-4">
                    <span className="bg-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm font-medium">
                      {article.category}
                    </span>
                    <span className="text-gray-500 text-sm">{article.readTime}</span>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-purple-600 transition-colors">
                    {article.title}
                  </h3>
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {article.excerpt}
                  </p>
                  <div className="flex items-center text-purple-600 font-semibold group-hover:text-purple-700 transition-colors">
                    <span>Read Article</span>
                    <ExternalLink className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-purple-600">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Transform Your Product Strategy?
              </h2>
              <p className="text-xl text-purple-100 mb-8 max-w-2xl mx-auto">
                Join thousands of product managers who are making better decisions with user feedback insights.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-white text-purple-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold transition-colors flex items-center gap-2">
                  Start Free Analysis
                  <ArrowRight className="w-4 h-4" />
                </button>
                <button className="border border-white text-white hover:bg-white hover:text-purple-600 px-8 py-3 rounded-lg font-semibold transition-colors">
                  Schedule Demo
                </button>
              </div>
            </motion.div>
          </div>
        </section>
      </div>

      <Footer />
    </>
  );
};
