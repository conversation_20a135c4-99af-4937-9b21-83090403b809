import React from 'react';
import { motion } from 'framer-motion';
import { ShoppingCart, CreditCard, Truck, Star, TrendingUp, CheckCircle, ArrowRight, Target } from 'lucide-react';
import { SEOHead } from '../components/SEOHead';
import { LazyImage } from '../components/LazyImage';
import { Footer } from '../components/Footer';

export const EcommerceAppsPage: React.FC = () => {
  const features = [
    {
      icon: <ShoppingCart className="w-8 h-8 text-blue-600" />,
      title: "Shopping Experience Analysis",
      description: "Analyze customer feedback on product discovery, search functionality, and overall shopping journey."
    },
    {
      icon: <CreditCard className="w-8 h-8 text-green-600" />,
      title: "Checkout & Payment Insights",
      description: "Understand payment flow issues, checkout abandonment reasons, and payment method preferences."
    },
    {
      icon: <Truck className="w-8 h-8 text-orange-600" />,
      title: "Delivery & Service Feedback",
      description: "Monitor customer satisfaction with shipping, delivery times, customer service, and return processes."
    },
    {
      icon: <Star className="w-8 h-8 text-yellow-600" />,
      title: "Product & Review Analysis",
      description: "Analyze product feedback, review quality, and customer satisfaction across different categories."
    }
  ];

  const ecommerceTypes = [
    {
      title: "Fashion & Apparel",
      description: "Size accuracy, style preferences, and return experience optimization",
      insights: ["Size guide effectiveness", "Style recommendation accuracy", "Return process feedback", "Material quality mentions"],
      metrics: "67% reduction in returns"
    },
    {
      title: "Electronics & Tech",
      description: "Product specifications, compatibility, and technical support analysis",
      insights: ["Specification accuracy", "Compatibility issues", "Technical support quality", "Warranty experience"],
      metrics: "85% better product satisfaction"
    },
    {
      title: "Home & Garden",
      description: "Product quality, assembly instructions, and delivery experience",
      insights: ["Assembly difficulty feedback", "Product durability mentions", "Delivery condition analysis", "Size expectations"],
      metrics: "72% improved delivery satisfaction"
    },
    {
      title: "Beauty & Health",
      description: "Product effectiveness, ingredient concerns, and skin compatibility",
      insights: ["Ingredient sensitivity feedback", "Product effectiveness reviews", "Packaging quality", "Skin type compatibility"],
      metrics: "58% better product matching"
    }
  ];

  const challenges = [
    {
      challenge: "Cart Abandonment",
      solution: "Identify specific checkout friction points from customer feedback",
      impact: "35% reduction in cart abandonment"
    },
    {
      challenge: "Return Rates",
      solution: "Analyze return reasons to improve product descriptions and sizing",
      impact: "45% decrease in return rates"
    },
    {
      challenge: "Customer Service",
      solution: "Monitor service quality feedback to improve support processes",
      impact: "80% improvement in service ratings"
    },
    {
      challenge: "Product Discovery",
      solution: "Optimize search and recommendation based on user feedback",
      impact: "60% better product findability"
    }
  ];

  const metrics = [
    { value: "200M+", label: "E-commerce Reviews Analyzed", description: "Comprehensive retail industry dataset" },
    { value: "93%", label: "Purchase Intent Accuracy", description: "E-commerce specific sentiment models" },
    { value: "55%", label: "Faster Issue Resolution", description: "Real-time customer feedback monitoring" },
    { value: "38%", label: "Higher Conversion Rates", description: "Data-driven shopping experience optimization" }
  ];

  return (
    <>
      <SEOHead 
        title="E-commerce Apps Analytics - AppReview.Today | Customer Experience Optimization"
        description="Optimize your e-commerce app with customer feedback analysis. Improve shopping experience, reduce cart abandonment, boost conversions and customer satisfaction."
        canonical="/ecommerce-apps"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebPage",
          "name": "E-commerce Apps Analytics - AppReview.Today",
          "description": "Customer experience optimization platform for e-commerce and retail mobile apps",
          "audience": {
            "@type": "Audience",
            "audienceType": "E-commerce Developers"
          },
          "about": {
            "@type": "Thing",
            "name": "E-commerce Analytics"
          }
        }}
      />
      
      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center"
            >
              <div className="flex justify-center mb-6">
                <ShoppingCart className="w-16 h-16 text-blue-400" />
              </div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Optimize Your <span className="text-blue-400">E-commerce App</span>
              </h1>
              <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto text-blue-100">
                Transform customer feedback into better shopping experiences. Reduce cart abandonment, 
                improve conversions, and build customer loyalty.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                  Analyze Customer Feedback
                </button>
                <button className="border border-blue-400 text-blue-400 hover:bg-blue-400 hover:text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                  View E-commerce Case Studies
                </button>
              </div>
            </motion.div>
          </div>
        </section>

        {/* E-commerce Features */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                E-commerce Focused Analytics
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Specialized insights for online retail and e-commerce mobile applications
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow"
                >
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      {feature.icon}
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-3">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* E-commerce Categories */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Tailored for Every E-commerce Category
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Specialized analytics for different types of online retail businesses
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {ecommerceTypes.map((type, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-gray-50 p-8 rounded-xl"
                >
                  <h3 className="text-xl font-bold text-gray-900 mb-3">
                    {type.title}
                  </h3>
                  <p className="text-gray-600 mb-6">
                    {type.description}
                  </p>
                  <div className="space-y-3 mb-6">
                    {type.insights.map((insight, insightIndex) => (
                      <div key={insightIndex} className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                        <span className="text-gray-700">{insight}</span>
                      </div>
                    ))}
                  </div>
                  <div className="bg-blue-100 p-4 rounded-lg">
                    <div className="flex items-center gap-2 text-blue-700 font-semibold">
                      <TrendingUp className="w-4 h-4" />
                      {type.metrics}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* E-commerce Challenges */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Solve E-commerce Challenges
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Address the most common issues facing e-commerce app developers
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {challenges.map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white p-8 rounded-xl shadow-lg"
                >
                  <div className="flex items-start gap-4 mb-4">
                    <Target className="w-6 h-6 text-red-600 flex-shrink-0 mt-1" />
                    <div>
                      <h3 className="text-lg font-bold text-gray-900 mb-2">
                        Challenge: {item.challenge}
                      </h3>
                      <p className="text-gray-600 mb-4">
                        {item.solution}
                      </p>
                      <div className="bg-green-100 p-3 rounded-lg">
                        <div className="flex items-center gap-2 text-green-700 font-semibold text-sm">
                          <TrendingUp className="w-4 h-4" />
                          {item.impact}
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* E-commerce Metrics */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Proven Results for E-commerce Apps
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                See how e-commerce businesses are improving customer satisfaction and sales
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {metrics.map((metric, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-gray-50 p-8 rounded-xl text-center"
                >
                  <div className="text-4xl font-bold text-blue-600 mb-2">
                    {metric.value}
                  </div>
                  <div className="text-lg font-semibold text-gray-900 mb-2">
                    {metric.label}
                  </div>
                  <div className="text-gray-600 text-sm">
                    {metric.description}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-blue-600">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Boost Your E-commerce Success?
              </h2>
              <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
                Join successful e-commerce businesses that are improving customer experience with data-driven insights.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold transition-colors flex items-center gap-2">
                  Start E-commerce Analysis
                  <ArrowRight className="w-4 h-4" />
                </button>
                <button className="border border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3 rounded-lg font-semibold transition-colors">
                  View E-commerce Demo
                </button>
              </div>
            </motion.div>
          </div>
        </section>
      </div>
      
      <Footer />
    </>
  );
};
