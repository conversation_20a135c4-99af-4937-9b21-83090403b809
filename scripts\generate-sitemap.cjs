const fs = require('fs');
const path = require('path');

// 网站基础URL - 部署时需要更新为实际域名
const SITE_URL = 'https://appreview.today';

// 静态页面配置
const staticPages = [
  {
    url: '/',
    changefreq: 'daily',
    priority: '1.0',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/pricing',
    changefreq: 'weekly',
    priority: '0.8',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/demo',
    changefreq: 'monthly',
    priority: '0.7',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/profile',
    changefreq: 'weekly',
    priority: '0.6',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/payment-success',
    changefreq: 'yearly',
    priority: '0.3',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/about',
    changefreq: 'monthly',
    priority: '0.8',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/faq',
    changefreq: 'weekly',
    priority: '0.8',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/how-it-works',
    changefreq: 'monthly',
    priority: '0.9',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/features',
    changefreq: 'weekly',
    priority: '0.9',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/use-cases',
    changefreq: 'weekly',
    priority: '0.8',
    lastmod: new Date().toISOString().split('T')[0]
  },

  {
    url: '/help',
    changefreq: 'weekly',
    priority: '0.9',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/getting-started',
    changefreq: 'monthly',
    priority: '0.9',
    lastmod: new Date().toISOString().split('T')[0]
  },

  {
    url: '/privacy',
    changefreq: 'monthly',
    priority: '0.7',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/terms',
    changefreq: 'monthly',
    priority: '0.7',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/security',
    changefreq: 'monthly',
    priority: '0.7',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/gdpr',
    changefreq: 'monthly',
    priority: '0.7',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/contact',
    changefreq: 'monthly',
    priority: '0.8',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/blog',
    changefreq: 'daily',
    priority: '0.9',
    lastmod: new Date().toISOString().split('T')[0]
  },
  // User Role Pages
  {
    url: '/for-product-managers',
    changefreq: 'weekly',
    priority: '0.8',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/for-marketers',
    changefreq: 'weekly',
    priority: '0.8',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/for-researchers',
    changefreq: 'weekly',
    priority: '0.8',
    lastmod: new Date().toISOString().split('T')[0]
  },

  // Industry Vertical Pages
  {
    url: '/gaming-apps',
    changefreq: 'weekly',
    priority: '0.8',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/ecommerce-apps',
    changefreq: 'weekly',
    priority: '0.8',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/fintech-apps',
    changefreq: 'weekly',
    priority: '0.8',
    lastmod: new Date().toISOString().split('T')[0]
  },

  // Sitemap Page
  {
    url: '/sitemap',
    changefreq: 'monthly',
    priority: '0.5',
    lastmod: new Date().toISOString().split('T')[0]
  }
];

// 未来可以添加的页面（当实现时取消注释）
const futurePagesTemplate = [
  // FAQ和帮助页面
  // { url: '/faq', changefreq: 'monthly', priority: '0.8' },
  // { url: '/help', changefreq: 'monthly', priority: '0.7' },
  // { url: '/how-it-works', changefreq: 'monthly', priority: '0.8' },
  
  // 法律页面
  // { url: '/privacy', changefreq: 'yearly', priority: '0.4' },
  // { url: '/terms', changefreq: 'yearly', priority: '0.4' },
  
  // 博客页面
  // { url: '/blog', changefreq: 'daily', priority: '0.9' },
  
  // 工具页面
  // { url: '/tools/rating-calculator', changefreq: 'monthly', priority: '0.7' },
  // { url: '/tools/sentiment-analyzer', changefreq: 'monthly', priority: '0.7' },
  
  // 行业页面
  // { url: '/industries/gaming', changefreq: 'weekly', priority: '0.6' },
  // { url: '/industries/ecommerce', changefreq: 'weekly', priority: '0.6' },
  // { url: '/industries/social', changefreq: 'weekly', priority: '0.6' },
  
  // 比较页面
  // { url: '/vs/appfollow', changefreq: 'monthly', priority: '0.6' },
  // { url: '/vs/sensor-tower', changefreq: 'monthly', priority: '0.6' },
];

// 生成XML sitemap内容
function generateSitemapXML(pages) {
  const urls = pages.map(page => `
  <url>
    <loc>${SITE_URL}${page.url}</loc>
    <lastmod>${page.lastmod}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`).join('');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urls}
</urlset>`;
}

// 生成robots.txt内容
function generateRobotsTxt() {
  return `User-agent: *
Allow: /

# Disallow private/admin areas
Disallow: /profile
Disallow: /payment-success
Disallow: /api/

# Allow important pages
Allow: /
Allow: /pricing
Allow: /demo

# Sitemap location
Sitemap: ${SITE_URL}/sitemap.xml

# Crawl delay (optional)
Crawl-delay: 1`;
}

// 主函数
async function generateSEOFiles() {
  try {
    // 确保public目录存在
    const publicDir = path.join(process.cwd(), 'public');
    if (!fs.existsSync(publicDir)) {
      fs.mkdirSync(publicDir, { recursive: true });
    }

    // 生成sitemap.xml
    const sitemapContent = generateSitemapXML(staticPages);
    const sitemapPath = path.join(publicDir, 'sitemap.xml');
    fs.writeFileSync(sitemapPath, sitemapContent, 'utf8');
    console.log('✅ sitemap.xml generated successfully');

    // 生成robots.txt
    const robotsContent = generateRobotsTxt();
    const robotsPath = path.join(publicDir, 'robots.txt');
    fs.writeFileSync(robotsPath, robotsContent, 'utf8');
    console.log('✅ robots.txt generated successfully');

    // 验证文件
    console.log('\n📊 Generated files:');
    console.log(`- sitemap.xml: ${fs.statSync(sitemapPath).size} bytes`);
    console.log(`- robots.txt: ${fs.statSync(robotsPath).size} bytes`);
    console.log(`\n🌐 Sitemap URL: ${SITE_URL}/sitemap.xml`);
    console.log(`🤖 Robots URL: ${SITE_URL}/robots.txt`);

  } catch (error) {
    console.error('❌ Error generating SEO files:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  generateSEOFiles();
}

module.exports = { generateSEOFiles, staticPages, SITE_URL };
