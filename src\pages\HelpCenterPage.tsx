import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Search, 
  Book, 
  MessageCircle, 
  Settings, 
  CreditCard, 
  Shield, 
  Zap,
  Users,
  FileText,
  ChevronRight,
  ExternalLink,
  Mail,
  Phone
} from 'lucide-react';
import { SEOHead } from '../components/SEOHead';
import { seoConfig } from '../lib/seo-config';

export function HelpCenterPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'All Topics', icon: Book, count: 24 },
    { id: 'getting-started', name: 'Getting Started', icon: Zap, count: 6 },
    { id: 'account', name: 'Account & Billing', icon: CreditCard, count: 5 },
    { id: 'analysis', name: 'Review Analysis', icon: MessageCircle, count: 8 },
    { id: 'integrations', name: 'Integrations', icon: Settings, count: 4 },
    { id: 'security', name: 'Security & Privacy', icon: Shield, count: 3 },
    { id: 'collaboration', name: 'Team & Collaboration', icon: Users, count: 2 }
  ];

  const helpArticles = [
    {
      id: 1,
      title: 'How to get started with AppReview.Today',
      description: 'Complete guide to setting up your first review analysis project',
      category: 'getting-started',
      readTime: '5 min read',
      popular: true
    },
    {
      id: 2,
      title: 'Understanding sentiment analysis results',
      description: 'Learn how to interpret AI-powered sentiment analysis data',
      category: 'analysis',
      readTime: '8 min read',
      popular: true
    },
    {
      id: 3,
      title: 'Setting up App Store Connect integration',
      description: 'Step-by-step guide to connect your App Store account',
      category: 'integrations',
      readTime: '10 min read',
      popular: false
    },
    {
      id: 4,
      title: 'Managing team permissions and roles',
      description: 'How to add team members and configure access levels',
      category: 'collaboration',
      readTime: '6 min read',
      popular: false
    },
    {
      id: 5,
      title: 'Billing and subscription management',
      description: 'Everything about plans, billing cycles, and upgrades',
      category: 'account',
      readTime: '4 min read',
      popular: true
    },
    {
      id: 6,
      title: 'Data security and privacy protection',
      description: 'How we protect your data and ensure compliance',
      category: 'security',
      readTime: '7 min read',
      popular: false
    },
    {
      id: 7,
      title: 'Exporting analysis reports',
      description: 'Different formats and customization options for reports',
      category: 'analysis',
      readTime: '5 min read',
      popular: true
    },
    {
      id: 8,
      title: 'API authentication and rate limits',
      description: 'Technical guide for developers using our API',
      category: 'integrations',
      readTime: '12 min read',
      popular: false
    }
  ];

  const filteredArticles = helpArticles.filter(article => {
    const matchesCategory = selectedCategory === 'all' || article.category === selectedCategory;
    const matchesSearch = article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         article.description.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const popularArticles = helpArticles.filter(article => article.popular);

  return (
    <>
      <SEOHead {...seoConfig.helpCenter} />
      
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <img src="/logo.svg" alt="AppReview.Today" className="h-8 w-8" />
                <span className="text-xl font-bold text-gray-900">AppReview.Today</span>
              </div>
              <button 
                onClick={() => window.history.back()}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Back to Home
              </button>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Book className="h-16 w-16 text-blue-600 mx-auto mb-6" />
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                Help <span className="text-blue-600">Center</span>
              </h1>
              <p className="text-xl text-gray-600 mb-8">
                Find answers to your questions and learn how to get the most out of AppReview.Today
              </p>
              
              {/* Search Bar */}
              <div className="relative max-w-2xl mx-auto">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search for help articles..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </motion.div>
          </div>
        </section>

        {/* Quick Actions */}
        <section className="py-8 px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="bg-white rounded-xl p-6 shadow-sm border hover:shadow-md transition-shadow cursor-pointer"
              >
                <Zap className="h-8 w-8 text-green-600 mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Getting Started Guide</h3>
                <p className="text-gray-600 mb-4">New to AppReview.Today? Start here for a complete walkthrough.</p>
                <div className="flex items-center text-green-600 font-medium">
                  <span>Start Guide</span>
                  <ChevronRight className="h-4 w-4 ml-1" />
                </div>
              </motion.div>



              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="bg-white rounded-xl p-6 shadow-sm border hover:shadow-md transition-shadow cursor-pointer"
              >
                <MessageCircle className="h-8 w-8 text-purple-600 mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Contact Support</h3>
                <p className="text-gray-600 mb-4">Can't find what you're looking for? Get in touch with our team.</p>
                <div className="flex items-center text-purple-600 font-medium">
                  <span>Contact Us</span>
                  <ChevronRight className="h-4 w-4 ml-1" />
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Categories and Articles */}
        <section className="py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col lg:flex-row gap-8">
              {/* Categories Sidebar */}
              <div className="lg:w-1/4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Categories</h3>
                <div className="space-y-2">
                  {categories.map((category) => {
                    const Icon = category.icon;
                    return (
                      <button
                        key={category.id}
                        onClick={() => setSelectedCategory(category.id)}
                        className={`w-full flex items-center justify-between p-3 rounded-lg text-left transition-colors ${
                          selectedCategory === category.id
                            ? 'bg-blue-50 text-blue-700 border border-blue-200'
                            : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50'
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <Icon className="h-5 w-5" />
                          <span className="font-medium">{category.name}</span>
                        </div>
                        <span className="text-sm text-gray-500">{category.count}</span>
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Articles */}
              <div className="lg:w-3/4">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {selectedCategory === 'all' ? 'All Articles' : categories.find(c => c.id === selectedCategory)?.name}
                  </h3>
                  <span className="text-sm text-gray-500">
                    {filteredArticles.length} article{filteredArticles.length !== 1 ? 's' : ''}
                  </span>
                </div>

                <div className="space-y-4">
                  {filteredArticles.map((article, index) => (
                    <motion.div
                      key={article.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: index * 0.1 }}
                      className="bg-white rounded-lg p-6 shadow-sm border hover:shadow-md transition-shadow cursor-pointer"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h4 className="text-lg font-semibold text-gray-900">{article.title}</h4>
                            {article.popular && (
                              <span className="px-2 py-1 text-xs font-medium bg-orange-100 text-orange-800 rounded-full">
                                Popular
                              </span>
                            )}
                          </div>
                          <p className="text-gray-600 mb-3">{article.description}</p>
                          <span className="text-sm text-gray-500">{article.readTime}</span>
                        </div>
                        <ChevronRight className="h-5 w-5 text-gray-400 ml-4 flex-shrink-0" />
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Contact Support */}
        <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Still Need Help?</h2>
            <p className="text-xl text-gray-600 mb-8">
              Our support team is here to help you succeed with AppReview.Today
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl mx-auto">
              <div className="bg-gray-50 rounded-xl p-6">
                <Mail className="h-8 w-8 text-blue-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Email Support</h3>
                <p className="text-gray-600 mb-4">Get detailed help via email</p>
                <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                  Send Email
                </button>
              </div>
              
              <div className="bg-gray-50 rounded-xl p-6">
                <MessageCircle className="h-8 w-8 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Live Chat</h3>
                <p className="text-gray-600 mb-4">Chat with our team in real-time</p>
                <button className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                  Start Chat
                </button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
}
