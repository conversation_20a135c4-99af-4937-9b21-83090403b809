import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  FileText, 
  Scale, 
  AlertTriangle, 
  CreditCard, 
  RefreshCw,
  Ban,
  Calendar,
  Mail,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { SEOHead } from '../components/SEOHead';
import { seoConfig } from '../lib/seo-config';

export function TermsOfServicePage() {
  const [expandedSection, setExpandedSection] = useState<string | null>(null);

  const sections = [
    {
      id: 'acceptance',
      title: 'Acceptance of Terms',
      icon: Scale,
      content: [
        {
          subtitle: 'Agreement to Terms',
          details: 'By accessing and using AppReview.Today, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.'
        },
        {
          subtitle: 'Eligibility',
          details: 'You must be at least 18 years old to use our service. By using our service, you represent and warrant that you have the legal capacity to enter into this agreement.'
        },
        {
          subtitle: 'Business Use',
          details: 'Our service is intended for business and professional use. You may not use our service for any illegal or unauthorized purpose.'
        }
      ]
    },
    {
      id: 'service-description',
      title: 'Service Description',
      icon: FileText,
      content: [
        {
          subtitle: 'App Review Analysis',
          details: 'AppReview.Today provides AI-powered analysis of publicly available app reviews from various platforms including App Store, Google Play, and other app marketplaces.'
        },
        {
          subtitle: 'Data Processing',
          details: 'Our service processes publicly available review data to generate insights, sentiment analysis, theme extraction, and competitive intelligence reports.'
        },
        {
          subtitle: 'Service Availability',
          details: 'We strive to maintain 99.9% uptime but do not guarantee uninterrupted service. Scheduled maintenance will be announced in advance when possible.'
        },
        {
          subtitle: 'Service Modifications',
          details: 'We reserve the right to modify, suspend, or discontinue any part of our service at any time with reasonable notice to users.'
        }
      ]
    },
    {
      id: 'user-responsibilities',
      title: 'User Responsibilities',
      icon: AlertTriangle,
      content: [
        {
          subtitle: 'Account Security',
          details: 'You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account.'
        },
        {
          subtitle: 'Accurate Information',
          details: 'You agree to provide accurate, current, and complete information when creating your account and to update such information as necessary.'
        },
        {
          subtitle: 'Prohibited Uses',
          details: 'You may not use our service to violate any laws, infringe on intellectual property rights, or engage in any harmful or malicious activities.'
        },
        {
          subtitle: 'Data Compliance',
          details: 'You are responsible for ensuring your use of our service complies with applicable data protection and privacy laws in your jurisdiction.'
        }
      ]
    },
    {
      id: 'payment-terms',
      title: 'Payment and Billing',
      icon: CreditCard,
      content: [
        {
          subtitle: 'Subscription Plans',
          details: 'Our service is offered through various subscription plans with different features and usage limits. Pricing is clearly displayed on our website.'
        },
        {
          subtitle: 'Payment Processing',
          details: 'Payments are processed securely through third-party payment processors. We do not store your payment information on our servers.'
        },
        {
          subtitle: 'Billing Cycles',
          details: 'Subscriptions are billed in advance on a monthly or annual basis, depending on your chosen plan. Billing occurs on the same date each cycle.'
        },
        {
          subtitle: 'Price Changes',
          details: 'We may change our pricing with 30 days advance notice. Existing subscribers will be notified before any price changes take effect.'
        }
      ]
    },
    {
      id: 'cancellation-refunds',
      title: 'Cancellation and Refunds',
      icon: RefreshCw,
      content: [
        {
          subtitle: 'Cancellation Policy',
          details: 'You may cancel your subscription at any time through your account settings. Cancellation will take effect at the end of your current billing period.'
        },
        {
          subtitle: 'Refund Policy',
          details: 'We offer a 14-day money-back guarantee for new subscribers. Refunds are processed within 5-10 business days to your original payment method.'
        },
        {
          subtitle: 'Pro-rated Refunds',
          details: 'For annual subscriptions cancelled within the first 30 days, we provide pro-rated refunds for the unused portion of your subscription.'
        },
        {
          subtitle: 'No Refund Situations',
          details: 'Refunds are not available for accounts terminated due to violation of these terms or for usage-based charges already incurred.'
        }
      ]
    },
    {
      id: 'termination',
      title: 'Account Termination',
      icon: Ban,
      content: [
        {
          subtitle: 'Termination by User',
          details: 'You may terminate your account at any time by cancelling your subscription and deleting your account through the account settings.'
        },
        {
          subtitle: 'Termination by Us',
          details: 'We may terminate or suspend your account immediately if you violate these terms, engage in fraudulent activity, or for other legitimate business reasons.'
        },
        {
          subtitle: 'Effect of Termination',
          details: 'Upon termination, your access to the service will cease immediately, and your data may be deleted according to our data retention policy.'
        },
        {
          subtitle: 'Data Export',
          details: 'Before termination, you may export your data through our data export feature. We are not responsible for data loss after account termination.'
        }
      ]
    },
    {
      id: 'intellectual-property',
      title: 'Intellectual Property',
      icon: FileText,
      content: [
        {
          subtitle: 'Our IP Rights',
          details: 'All content, features, and functionality of our service are owned by AppReview.Today and are protected by copyright, trademark, and other intellectual property laws.'
        },
        {
          subtitle: 'User Content',
          details: 'You retain ownership of any content you provide to our service. By using our service, you grant us a license to use your content to provide our services.'
        },
        {
          subtitle: 'Generated Reports',
          details: 'Reports and analyses generated by our service are owned by you, but our underlying algorithms and methodologies remain our intellectual property.'
        },
        {
          subtitle: 'Trademark Usage',
          details: 'You may not use our trademarks, logos, or brand names without our prior written consent.'
        }
      ]
    }
  ];

  const lastUpdated = 'January 15, 2025';

  return (
    <>
      <SEOHead {...seoConfig.termsOfService} />
      
      <div className="min-h-screen bg-gradient-to-br from-[#0A1128] via-[#0F1B3C] to-[#0A1128]">
        {/* Header */}
        <header className="border-b border-white/10 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <img src="/logo.svg" alt="AppReview.Today" className="h-8 w-8" />
                <span className="text-xl font-bold text-white">AppReview.Today</span>
              </div>
              <button
                onClick={() => window.history.back()}
                className="px-4 py-2 text-sm font-medium text-white bg-white/10 rounded-lg hover:bg-white/20 transition-colors backdrop-blur-sm border border-white/20"
              >
                Back to Home
              </button>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Scale className="h-16 w-16 text-blue-600 mx-auto mb-6" />
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                Terms of <span className="text-blue-600">Service</span>
              </h1>
              <p className="text-xl text-gray-600 mb-6">
                Please read these terms carefully before using our service. They govern your use of AppReview.Today.
              </p>
              <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
                <Calendar className="h-4 w-4" />
                <span>Last updated: {lastUpdated}</span>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Quick Summary */}
        <section className="py-8 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="bg-blue-50 border border-blue-200 rounded-xl p-6 mb-8">
              <h2 className="text-lg font-semibold text-blue-900 mb-4">Terms Summary</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
                <div className="flex items-start space-x-2">
                  <Scale className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <span>Fair and transparent terms for all users</span>
                </div>
                <div className="flex items-start space-x-2">
                  <RefreshCw className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <span>14-day money-back guarantee</span>
                </div>
                <div className="flex items-start space-x-2">
                  <FileText className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <span>Clear service descriptions and limitations</span>
                </div>
                <div className="flex items-start space-x-2">
                  <CreditCard className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <span>Flexible billing and cancellation options</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Detailed Sections */}
        <section className="py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="space-y-6">
              {sections.map((section, index) => {
                const Icon = section.icon;
                const isExpanded = expandedSection === section.id;
                
                return (
                  <motion.div
                    key={section.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="bg-white rounded-xl shadow-sm border"
                  >
                    <button
                      onClick={() => setExpandedSection(isExpanded ? null : section.id)}
                      className="w-full flex items-center justify-between p-6 text-left hover:bg-gray-50"
                    >
                      <div className="flex items-center space-x-4">
                        <Icon className="h-6 w-6 text-blue-600" />
                        <h3 className="text-lg font-semibold text-gray-900">{section.title}</h3>
                      </div>
                      {isExpanded ? 
                        <ChevronDown className="h-5 w-5 text-gray-400" /> :
                        <ChevronRight className="h-5 w-5 text-gray-400" />
                      }
                    </button>
                    
                    {isExpanded && (
                      <div className="border-t border-gray-200 p-6 bg-gray-50">
                        <div className="space-y-6">
                          {section.content.map((item, itemIndex) => (
                            <div key={itemIndex}>
                              <h4 className="font-semibold text-gray-900 mb-2">{item.subtitle}</h4>
                              <p className="text-gray-600 leading-relaxed">{item.details}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </motion.div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Contact Information */}
        <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Questions About Our Terms?</h2>
            <p className="text-xl text-gray-600 mb-8">
              If you have any questions about these Terms of Service, please contact us.
            </p>
            
            <div className="bg-gray-50 rounded-xl p-6 max-w-md mx-auto">
              <Mail className="h-8 w-8 text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Legal Team</h3>
              <p className="text-gray-600 mb-4"><EMAIL></p>
              <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                Contact Legal Team
              </button>
            </div>
          </div>
        </section>
      </div>
    </>
  );
}
