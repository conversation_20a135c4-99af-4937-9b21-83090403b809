import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Code, 
  Book, 
  Key, 
  Zap, 
  Shield, 
  Globe,
  Copy,
  ExternalLink,
  ChevronRight,
  ChevronDown,
  Terminal,
  Database,
  Webhook,
  Settings
} from 'lucide-react';
import { SEOHead } from '../components/SEOHead';
import { seoConfig } from '../lib/seo-config';

export function APIDocumentationPage() {
  const [activeSection, setActiveSection] = useState('overview');
  const [expandedEndpoint, setExpandedEndpoint] = useState<string | null>(null);

  const sections = [
    { id: 'overview', name: 'Overview', icon: Book },
    { id: 'authentication', name: 'Authentication', icon: Key },
    { id: 'endpoints', name: 'API Endpoints', icon: Globe },
    { id: 'webhooks', name: 'Webhooks', icon: Webhook },
    { id: 'sdks', name: 'SDKs & Libraries', icon: Code },
    { id: 'rate-limits', name: 'Rate Limits', icon: Shield }
  ];

  const endpoints = [
    {
      id: 'get-analysis',
      method: 'GET',
      path: '/api/v1/analysis/{id}',
      title: 'Get Analysis Results',
      description: 'Retrieve detailed analysis results for a specific analysis job',
      parameters: [
        { name: 'id', type: 'string', required: true, description: 'Analysis job ID' }
      ],
      response: {
        status: 200,
        example: `{
  "id": "analysis_123",
  "status": "completed",
  "app_id": "app_456",
  "sentiment": {
    "positive": 0.65,
    "neutral": 0.25,
    "negative": 0.10
  },
  "themes": [
    {
      "name": "User Interface",
      "sentiment": 0.72,
      "mentions": 145
    }
  ],
  "created_at": "2025-01-15T10:30:00Z",
  "completed_at": "2025-01-15T10:35:00Z"
}`
      }
    },
    {
      id: 'create-analysis',
      method: 'POST',
      path: '/api/v1/analysis',
      title: 'Create New Analysis',
      description: 'Start a new review analysis job for an app',
      parameters: [
        { name: 'app_id', type: 'string', required: true, description: 'Target app ID' },
        { name: 'date_from', type: 'string', required: false, description: 'Start date (ISO 8601)' },
        { name: 'date_to', type: 'string', required: false, description: 'End date (ISO 8601)' },
        { name: 'platforms', type: 'array', required: false, description: 'Platforms to analyze' }
      ],
      response: {
        status: 201,
        example: `{
  "id": "analysis_789",
  "status": "processing",
  "app_id": "app_456",
  "estimated_completion": "2025-01-15T10:35:00Z",
  "created_at": "2025-01-15T10:30:00Z"
}`
      }
    },
    {
      id: 'list-apps',
      method: 'GET',
      path: '/api/v1/apps',
      title: 'List Apps',
      description: 'Get a list of all apps in your account',
      parameters: [
        { name: 'limit', type: 'integer', required: false, description: 'Number of results (max 100)' },
        { name: 'offset', type: 'integer', required: false, description: 'Pagination offset' }
      ],
      response: {
        status: 200,
        example: `{
  "apps": [
    {
      "id": "app_456",
      "name": "My Awesome App",
      "platform": "ios",
      "app_store_id": "*********",
      "created_at": "2025-01-10T09:00:00Z"
    }
  ],
  "total": 1,
  "limit": 50,
  "offset": 0
}`
      }
    }
  ];

  const sdks = [
    {
      language: 'Python',
      description: 'Official Python SDK with full API coverage',
      installation: 'pip install appreview-today',
      example: `from appreview_today import Client

client = Client(api_key="your_api_key")
analysis = client.create_analysis(app_id="app_456")
print(f"Analysis ID: {analysis.id}")`
    },
    {
      language: 'Node.js',
      description: 'JavaScript/TypeScript SDK for Node.js applications',
      installation: 'npm install @appreview-today/sdk',
      example: `import { AppReviewClient } from '@appreview-today/sdk';

const client = new AppReviewClient('your_api_key');
const analysis = await client.createAnalysis({ appId: 'app_456' });
console.log('Analysis ID:', analysis.id);`
    },
    {
      language: 'PHP',
      description: 'PHP SDK with Composer support',
      installation: 'composer require appreview-today/php-sdk',
      example: `<?php
use AppReviewToday\\Client;

$client = new Client('your_api_key');
$analysis = $client->createAnalysis(['app_id' => 'app_456']);
echo "Analysis ID: " . $analysis->id;`
    }
  ];

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <>
      <SEOHead {...seoConfig.apiDocs} />
      
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <img src="/logo.svg" alt="AppReview.Today" className="h-8 w-8" />
                <span className="text-xl font-bold text-gray-900">AppReview.Today</span>
              </div>
              <button 
                onClick={() => window.history.back()}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Back to Home
              </button>
            </div>
          </div>
        </header>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Sidebar */}
            <div className="lg:w-1/4">
              <div className="sticky top-8">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Documentation</h2>
                <nav className="space-y-2">
                  {sections.map((section) => {
                    const Icon = section.icon;
                    return (
                      <button
                        key={section.id}
                        onClick={() => setActiveSection(section.id)}
                        className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                          activeSection === section.id
                            ? 'bg-blue-50 text-blue-700 border border-blue-200'
                            : 'text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <Icon className="h-5 w-5" />
                        <span className="font-medium">{section.name}</span>
                      </button>
                    );
                  })}
                </nav>
              </div>
            </div>

            {/* Main Content */}
            <div className="lg:w-3/4">
              {activeSection === 'overview' && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="bg-white rounded-xl p-8 shadow-sm border">
                    <div className="flex items-center space-x-4 mb-6">
                      <Terminal className="h-8 w-8 text-blue-600" />
                      <h1 className="text-3xl font-bold text-gray-900">API Documentation</h1>
                    </div>
                    
                    <p className="text-lg text-gray-600 mb-8">
                      The AppReview.Today API allows you to integrate review analysis capabilities directly into your applications and workflows.
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                      <div className="bg-blue-50 rounded-lg p-6">
                        <Zap className="h-8 w-8 text-blue-600 mb-4" />
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">RESTful API</h3>
                        <p className="text-gray-600">Simple HTTP-based API with JSON responses</p>
                      </div>
                      
                      <div className="bg-green-50 rounded-lg p-6">
                        <Shield className="h-8 w-8 text-green-600 mb-4" />
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">Secure</h3>
                        <p className="text-gray-600">API key authentication with rate limiting</p>
                      </div>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Base URL</h3>
                      <div className="bg-gray-900 rounded-lg p-4 flex items-center justify-between">
                        <code className="text-green-400 font-mono">https://api.appreview.today</code>
                        <button 
                          onClick={() => copyToClipboard('https://api.appreview.today')}
                          className="text-gray-400 hover:text-white"
                        >
                          <Copy className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {activeSection === 'authentication' && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="bg-white rounded-xl p-8 shadow-sm border">
                    <h1 className="text-3xl font-bold text-gray-900 mb-6">Authentication</h1>
                    
                    <p className="text-lg text-gray-600 mb-8">
                      AppReview.Today uses API keys for authentication. Include your API key in the Authorization header of all requests.
                    </p>

                    <div className="space-y-6">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Getting Your API Key</h3>
                        <ol className="list-decimal list-inside space-y-2 text-gray-600">
                          <li>Log in to your AppReview.Today dashboard</li>
                          <li>Navigate to Settings → API Keys</li>
                          <li>Click "Generate New API Key"</li>
                          <li>Copy and securely store your API key</li>
                        </ol>
                      </div>

                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Making Authenticated Requests</h3>
                        <div className="bg-gray-900 rounded-lg p-4">
                          <pre className="text-green-400 font-mono text-sm overflow-x-auto">
{`curl -H "Authorization: Bearer YOUR_API_KEY" \\
     -H "Content-Type: application/json" \\
     https://api.appreview.today/v1/apps`}
                          </pre>
                        </div>
                      </div>

                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <h4 className="font-semibold text-yellow-800 mb-2">Security Best Practices</h4>
                        <ul className="list-disc list-inside space-y-1 text-yellow-700 text-sm">
                          <li>Never expose API keys in client-side code</li>
                          <li>Use environment variables to store API keys</li>
                          <li>Rotate API keys regularly</li>
                          <li>Use different API keys for different environments</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {activeSection === 'endpoints' && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="bg-white rounded-xl p-8 shadow-sm border">
                    <h1 className="text-3xl font-bold text-gray-900 mb-6">API Endpoints</h1>
                    
                    <div className="space-y-6">
                      {endpoints.map((endpoint) => (
                        <div key={endpoint.id} className="border border-gray-200 rounded-lg">
                          <button
                            onClick={() => setExpandedEndpoint(
                              expandedEndpoint === endpoint.id ? null : endpoint.id
                            )}
                            className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50"
                          >
                            <div className="flex items-center space-x-4">
                              <span className={`px-2 py-1 text-xs font-semibold rounded ${
                                endpoint.method === 'GET' ? 'bg-blue-100 text-blue-800' :
                                endpoint.method === 'POST' ? 'bg-green-100 text-green-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {endpoint.method}
                              </span>
                              <code className="font-mono text-sm text-gray-700">{endpoint.path}</code>
                              <span className="font-medium text-gray-900">{endpoint.title}</span>
                            </div>
                            {expandedEndpoint === endpoint.id ? 
                              <ChevronDown className="h-5 w-5 text-gray-400" /> :
                              <ChevronRight className="h-5 w-5 text-gray-400" />
                            }
                          </button>
                          
                          {expandedEndpoint === endpoint.id && (
                            <div className="border-t border-gray-200 p-4 bg-gray-50">
                              <p className="text-gray-600 mb-4">{endpoint.description}</p>
                              
                              {endpoint.parameters.length > 0 && (
                                <div className="mb-4">
                                  <h4 className="font-semibold text-gray-900 mb-2">Parameters</h4>
                                  <div className="overflow-x-auto">
                                    <table className="min-w-full text-sm">
                                      <thead>
                                        <tr className="border-b border-gray-200">
                                          <th className="text-left py-2 font-medium text-gray-900">Name</th>
                                          <th className="text-left py-2 font-medium text-gray-900">Type</th>
                                          <th className="text-left py-2 font-medium text-gray-900">Required</th>
                                          <th className="text-left py-2 font-medium text-gray-900">Description</th>
                                        </tr>
                                      </thead>
                                      <tbody>
                                        {endpoint.parameters.map((param, index) => (
                                          <tr key={index} className="border-b border-gray-100">
                                            <td className="py-2 font-mono text-blue-600">{param.name}</td>
                                            <td className="py-2 text-gray-600">{param.type}</td>
                                            <td className="py-2">
                                              <span className={`px-2 py-1 text-xs rounded ${
                                                param.required ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-600'
                                              }`}>
                                                {param.required ? 'Required' : 'Optional'}
                                              </span>
                                            </td>
                                            <td className="py-2 text-gray-600">{param.description}</td>
                                          </tr>
                                        ))}
                                      </tbody>
                                    </table>
                                  </div>
                                </div>
                              )}
                              
                              <div>
                                <h4 className="font-semibold text-gray-900 mb-2">Response Example</h4>
                                <div className="bg-gray-900 rounded-lg p-4">
                                  <pre className="text-green-400 font-mono text-sm overflow-x-auto">
                                    {endpoint.response.example}
                                  </pre>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              )}

              {activeSection === 'sdks' && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="bg-white rounded-xl p-8 shadow-sm border">
                    <h1 className="text-3xl font-bold text-gray-900 mb-6">SDKs & Libraries</h1>

                    <p className="text-lg text-gray-600 mb-8">
                      Official SDKs are available for popular programming languages to make integration easier.
                    </p>

                    <div className="space-y-8">
                      {sdks.map((sdk, index) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-6">
                          <h3 className="text-xl font-semibold text-gray-900 mb-2">{sdk.language}</h3>
                          <p className="text-gray-600 mb-4">{sdk.description}</p>

                          <div className="space-y-4">
                            <div>
                              <h4 className="font-medium text-gray-900 mb-2">Installation</h4>
                              <div className="bg-gray-900 rounded-lg p-3">
                                <code className="text-green-400 font-mono text-sm">{sdk.installation}</code>
                              </div>
                            </div>

                            <div>
                              <h4 className="font-medium text-gray-900 mb-2">Example Usage</h4>
                              <div className="bg-gray-900 rounded-lg p-4">
                                <pre className="text-green-400 font-mono text-sm overflow-x-auto">
                                  {sdk.example}
                                </pre>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              )}

              {activeSection === 'rate-limits' && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="bg-white rounded-xl p-8 shadow-sm border">
                    <h1 className="text-3xl font-bold text-gray-900 mb-6">Rate Limits</h1>

                    <p className="text-lg text-gray-600 mb-8">
                      To ensure fair usage and optimal performance, our API implements rate limiting based on your subscription plan.
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                      <div className="bg-gray-50 rounded-lg p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">Free Plan</h3>
                        <div className="space-y-2 text-sm text-gray-600">
                          <p>100 requests/hour</p>
                          <p>1,000 requests/month</p>
                          <p>5 concurrent requests</p>
                        </div>
                      </div>

                      <div className="bg-blue-50 rounded-lg p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">Pro Plan</h3>
                        <div className="space-y-2 text-sm text-gray-600">
                          <p>1,000 requests/hour</p>
                          <p>50,000 requests/month</p>
                          <p>20 concurrent requests</p>
                        </div>
                      </div>

                      <div className="bg-purple-50 rounded-lg p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">Enterprise</h3>
                        <div className="space-y-2 text-sm text-gray-600">
                          <p>Custom limits</p>
                          <p>Unlimited requests</p>
                          <p>Dedicated resources</p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <h4 className="font-semibold text-yellow-800 mb-2">Rate Limit Headers</h4>
                      <p className="text-yellow-700 text-sm mb-3">
                        Every API response includes headers to help you track your usage:
                      </p>
                      <div className="bg-gray-900 rounded-lg p-3">
                        <pre className="text-green-400 font-mono text-sm">
{`X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1642694400`}
                        </pre>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
