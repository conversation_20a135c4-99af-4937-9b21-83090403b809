import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  AlertTriangle, 
  Search, 
  CheckCircle, 
  XCircle, 
  Clock,
  RefreshCw,
  Wifi,
  Key,
  Database,
  Settings,
  MessageCircle,
  ExternalLink,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { SEOHead } from '../components/SEOHead';
import { seoConfig } from '../lib/seo-config';

export function TroubleshootingPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [expandedIssue, setExpandedIssue] = useState<string | null>(null);

  const categories = [
    { id: 'all', name: 'All Issues', icon: AlertTriangle },
    { id: 'authentication', name: 'Authentication', icon: Key },
    { id: 'analysis', name: 'Analysis Issues', icon: Database },
    { id: 'connectivity', name: 'Connectivity', icon: Wifi },
    { id: 'performance', name: 'Performance', icon: Clock },
    { id: 'integration', name: 'Integrations', icon: Settings }
  ];

  const commonIssues = [
    {
      id: 'auth-invalid-key',
      title: 'Invalid API Key Error',
      category: 'authentication',
      severity: 'high',
      description: 'Getting "Invalid API Key" error when making API requests',
      symptoms: [
        '401 Unauthorized response',
        'Error message: "Invalid API Key"',
        'Unable to access any API endpoints'
      ],
      solutions: [
        'Verify your API key is correct and hasn\'t been regenerated',
        'Check that you\'re including the "Bearer " prefix in the Authorization header',
        'Ensure there are no extra spaces or characters in your API key',
        'Try generating a new API key from your dashboard'
      ],
      code: `// Correct API key usage
const response = await fetch('https://api.appreview.today/v1/apps', {
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  }
});`
    },
    {
      id: 'analysis-stuck',
      title: 'Analysis Job Stuck in Processing',
      category: 'analysis',
      severity: 'medium',
      description: 'Analysis job remains in "processing" status for extended periods',
      symptoms: [
        'Status shows "processing" for more than 30 minutes',
        'No progress updates or completion notifications',
        'Unable to retrieve results'
      ],
      solutions: [
        'Check if the app has sufficient reviews to analyze (minimum 10 reviews)',
        'Verify the date range isn\'t too large (max 1 year)',
        'Try creating a new analysis with a smaller date range',
        'Contact support if the issue persists beyond 1 hour'
      ],
      code: `// Check analysis status
const analysis = await client.getAnalysis('analysis_id');
console.log('Status:', analysis.status);
console.log('Progress:', analysis.progress);`
    },
    {
      id: 'rate-limit-exceeded',
      title: 'Rate Limit Exceeded',
      category: 'connectivity',
      severity: 'medium',
      description: 'Receiving 429 Too Many Requests error',
      symptoms: [
        '429 HTTP status code',
        'Error message about rate limiting',
        'Requests being rejected'
      ],
      solutions: [
        'Check your current rate limit usage in the dashboard',
        'Implement exponential backoff in your requests',
        'Consider upgrading your plan for higher limits',
        'Spread out your requests over time'
      ],
      code: `// Implement retry with exponential backoff
async function makeRequestWithRetry(url, options, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const response = await fetch(url, options);
      if (response.status === 429) {
        const delay = Math.pow(2, i) * 1000; // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }
      return response;
    } catch (error) {
      if (i === maxRetries - 1) throw error;
    }
  }
}`
    },
    {
      id: 'slow-response',
      title: 'Slow API Response Times',
      category: 'performance',
      severity: 'low',
      description: 'API requests taking longer than expected to complete',
      symptoms: [
        'Response times over 10 seconds',
        'Timeout errors in applications',
        'Poor user experience'
      ],
      solutions: [
        'Check your internet connection stability',
        'Verify you\'re using the correct API endpoint region',
        'Reduce the scope of your analysis requests',
        'Implement proper timeout handling in your code'
      ],
      code: `// Set appropriate timeouts
const controller = new AbortController();
const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

try {
  const response = await fetch(url, {
    signal: controller.signal,
    ...options
  });
  clearTimeout(timeoutId);
  return response;
} catch (error) {
  if (error.name === 'AbortError') {
    console.log('Request timed out');
  }
  throw error;
}`
    },
    {
      id: 'integration-webhook-failed',
      title: 'Webhook Delivery Failures',
      category: 'integration',
      severity: 'medium',
      description: 'Webhooks not being delivered to your endpoint',
      symptoms: [
        'Missing webhook notifications',
        'Webhook delivery failures in dashboard',
        'Events not triggering in your application'
      ],
      solutions: [
        'Verify your webhook endpoint is publicly accessible',
        'Check that your endpoint returns a 200 status code',
        'Ensure your endpoint can handle the webhook payload format',
        'Test your endpoint with a webhook testing tool'
      ],
      code: `// Example webhook endpoint (Express.js)
app.post('/webhooks/appreview', (req, res) => {
  try {
    const event = req.body;
    console.log('Received webhook:', event.type);
    
    // Process the webhook event
    handleWebhookEvent(event);
    
    // Always respond with 200
    res.status(200).send('OK');
  } catch (error) {
    console.error('Webhook error:', error);
    res.status(500).send('Error');
  }
});`
    },
    {
      id: 'data-inconsistency',
      title: 'Inconsistent Analysis Results',
      category: 'analysis',
      severity: 'medium',
      description: 'Getting different results for the same analysis parameters',
      symptoms: [
        'Sentiment scores varying between runs',
        'Different theme extraction results',
        'Inconsistent review counts'
      ],
      solutions: [
        'Check if new reviews were added between analysis runs',
        'Verify you\'re using the same date range and filters',
        'Ensure your app configuration hasn\'t changed',
        'Contact support with specific analysis IDs for investigation'
      ],
      code: `// Ensure consistent analysis parameters
const analysisParams = {
  app_id: 'app_123',
  date_from: '2025-01-01T00:00:00Z',
  date_to: '2025-01-31T23:59:59Z',
  platforms: ['ios', 'android'],
  include_replies: false
};

const analysis = await client.createAnalysis(analysisParams);`
    }
  ];

  const filteredIssues = commonIssues.filter(issue => {
    const matchesCategory = selectedCategory === 'all' || issue.category === selectedCategory;
    const matchesSearch = issue.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         issue.description.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <>
      <SEOHead {...seoConfig.troubleshooting} />
      
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <img src="/logo.svg" alt="AppReview.Today" className="h-8 w-8" />
                <span className="text-xl font-bold text-gray-900">AppReview.Today</span>
              </div>
              <button 
                onClick={() => window.history.back()}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Back to Home
              </button>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <AlertTriangle className="h-16 w-16 text-orange-600 mx-auto mb-6" />
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                Troubleshooting <span className="text-orange-600">Guide</span>
              </h1>
              <p className="text-xl text-gray-600 mb-8">
                Find solutions to common issues and get your AppReview.Today integration working smoothly
              </p>
              
              {/* Search Bar */}
              <div className="relative max-w-2xl mx-auto">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search for issues or error messages..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
              </div>
            </motion.div>
          </div>
        </section>

        {/* Quick Actions */}
        <section className="py-8 px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="bg-white rounded-xl p-6 shadow-sm border hover:shadow-md transition-shadow cursor-pointer"
              >
                <RefreshCw className="h-8 w-8 text-blue-600 mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">System Status</h3>
                <p className="text-gray-600 mb-4">Check if there are any ongoing service issues or maintenance.</p>
                <div className="flex items-center text-blue-600 font-medium">
                  <span>Check Status</span>
                  <ExternalLink className="h-4 w-4 ml-1" />
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-white rounded-xl p-6 shadow-sm border hover:shadow-md transition-shadow cursor-pointer"
              >
                <MessageCircle className="h-8 w-8 text-green-600 mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Contact Support</h3>
                <p className="text-gray-600 mb-4">Can't find a solution? Our support team is here to help.</p>
                <div className="flex items-center text-green-600 font-medium">
                  <span>Get Help</span>
                  <ChevronRight className="h-4 w-4 ml-1" />
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="bg-white rounded-xl p-6 shadow-sm border hover:shadow-md transition-shadow cursor-pointer"
              >
                <Settings className="h-8 w-8 text-purple-600 mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">API Documentation</h3>
                <p className="text-gray-600 mb-4">Review technical documentation and API reference.</p>
                <div className="flex items-center text-purple-600 font-medium">
                  <span>View Docs</span>
                  <ExternalLink className="h-4 w-4 ml-1" />
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Categories and Issues */}
        <section className="py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col lg:flex-row gap-8">
              {/* Categories Sidebar */}
              <div className="lg:w-1/4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Categories</h3>
                <div className="space-y-2">
                  {categories.map((category) => {
                    const Icon = category.icon;
                    return (
                      <button
                        key={category.id}
                        onClick={() => setSelectedCategory(category.id)}
                        className={`w-full flex items-center space-x-3 p-3 rounded-lg text-left transition-colors ${
                          selectedCategory === category.id
                            ? 'bg-orange-50 text-orange-700 border border-orange-200'
                            : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50'
                        }`}
                      >
                        <Icon className="h-5 w-5" />
                        <span className="font-medium">{category.name}</span>
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Issues */}
              <div className="lg:w-3/4">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {selectedCategory === 'all' ? 'All Issues' : categories.find(c => c.id === selectedCategory)?.name}
                  </h3>
                  <span className="text-sm text-gray-500">
                    {filteredIssues.length} issue{filteredIssues.length !== 1 ? 's' : ''}
                  </span>
                </div>

                <div className="space-y-4">
                  {filteredIssues.map((issue, index) => (
                    <motion.div
                      key={issue.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: index * 0.1 }}
                      className="bg-white rounded-lg shadow-sm border"
                    >
                      <button
                        onClick={() => setExpandedIssue(expandedIssue === issue.id ? null : issue.id)}
                        className="w-full flex items-center justify-between p-6 text-left hover:bg-gray-50"
                      >
                        <div className="flex items-start space-x-4">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <h4 className="text-lg font-semibold text-gray-900">{issue.title}</h4>
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSeverityColor(issue.severity)}`}>
                                {issue.severity}
                              </span>
                            </div>
                            <p className="text-gray-600">{issue.description}</p>
                          </div>
                        </div>
                        {expandedIssue === issue.id ? 
                          <ChevronDown className="h-5 w-5 text-gray-400 flex-shrink-0" /> :
                          <ChevronRight className="h-5 w-5 text-gray-400 flex-shrink-0" />
                        }
                      </button>
                      
                      {expandedIssue === issue.id && (
                        <div className="border-t border-gray-200 p-6 bg-gray-50">
                          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                              <h5 className="font-semibold text-gray-900 mb-3">Symptoms</h5>
                              <ul className="space-y-2">
                                {issue.symptoms.map((symptom, idx) => (
                                  <li key={idx} className="flex items-start space-x-2">
                                    <XCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                                    <span className="text-sm text-gray-600">{symptom}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>
                            
                            <div>
                              <h5 className="font-semibold text-gray-900 mb-3">Solutions</h5>
                              <ul className="space-y-2">
                                {issue.solutions.map((solution, idx) => (
                                  <li key={idx} className="flex items-start space-x-2">
                                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                                    <span className="text-sm text-gray-600">{solution}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>
                          </div>
                          
                          {issue.code && (
                            <div className="mt-6">
                              <h5 className="font-semibold text-gray-900 mb-3">Code Example</h5>
                              <div className="bg-gray-900 rounded-lg p-4">
                                <pre className="text-green-400 font-mono text-sm overflow-x-auto">
                                  {issue.code}
                                </pre>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Still Need Help */}
        <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Still Having Issues?</h2>
            <p className="text-xl text-gray-600 mb-8">
              Our technical support team is ready to help you resolve any problems
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl mx-auto">
              <div className="bg-gray-50 rounded-xl p-6">
                <MessageCircle className="h-8 w-8 text-blue-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Live Chat Support</h3>
                <p className="text-gray-600 mb-4">Get real-time help from our technical team</p>
                <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                  Start Chat
                </button>
              </div>
              
              <div className="bg-gray-50 rounded-xl p-6">
                <ExternalLink className="h-8 w-8 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Submit Ticket</h3>
                <p className="text-gray-600 mb-4">Create a detailed support ticket for complex issues</p>
                <button className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                  Create Ticket
                </button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
}
